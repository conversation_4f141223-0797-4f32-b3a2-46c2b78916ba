from django.urls import path, include, re_path
from django.contrib.admin.views.decorators import staff_member_required
from . import views, upload_data, download_data

urlpatterns = [
    path('auditoriums/', include(([
        #GET in use inn the drop menu
        path('', views.AuditoriumList.as_view()),
        path('<pk>/', views.AuditoriumDetail.as_view()),
        ], 'auditorium'))),

    path('auditorium_resources/', include(([
        path('', views.AuditoriumResourceList.as_view()),
        path('<pk>/', views.AuditoriumResourceDetail.as_view()),
    ]))),

    path('sessions/', include(([
        path('', views.SessionList.as_view()),
        path('<pk>/', views.SessionDetail.as_view()),
    ]))),

    path('videos/', include(([
        path('', views.VideoList.as_view()),
        path('<pk>/', views.VideoDetail.as_view()),
        ], 'auditorium_videos'))),

    path('video_subtitles/', include(([
        path('', views.VideoSubtitleFilesList.as_view()),
        path('<pk>/', views.VideoSubtitlesFileDetail.as_view()),
    ]))),

    # Filtering Paths
    path('auditorium_resources_filter/auditorium/<str:auditorium>/', views.auditorium_resources_filter_by_auditorium, name='auditorium_resources_filter_by_auditorium'),
    path('sessions_filter/auditorium/<str:auditorium>/', views.sessions_filter_by_auditorium, name='sessions_filter_by_auditorium'),
    path('sessions_filter/tile/<str:tile>/', views.sessions_filter_by_tile, name='sessions_filter_by_tile'),

    # Meant for scheduler feature... (not in use)
    path('get_aud_dates/', views.get_aud_dates, name='get_aud_dates'),
    path('get_auditoriums_by_date/', views.get_auditoriums_by_date, name='get_auditoriums_by_date'),
    path('get_current_date/', views.get_current_date, name='get_current_date'),
    
    # Data Upload Paths
    path('auditorium_upload/', staff_member_required(upload_data.AuditoriumUpload.as_view(), login_url='/login_client/'), name='auditorium_upload'),
    path('auditorium_image_upload/', staff_member_required(upload_data.AuditoriumImageUpload.as_view(), login_url='/login_client/'), name='auditorium_image_upload'),
    path('auditorium_video_upload/', staff_member_required(upload_data.AuditoriumVideoUpload.as_view(), login_url='/login_client/'), name='auditorium_video_upload'),
    path('auditorium_session_upload/', staff_member_required(upload_data.AuditoriumSessionUpload.as_view(), login_url='/login_client/'), name='auditorium_session_upload'),

    path('video_file_upload/', staff_member_required(upload_data.VideoFileUpload.as_view(), login_url='/login_client/'), name='video_file_upload'),

    # Data Download Paths
    re_path(r'^event_download/export_auditorium_data/$', staff_member_required(download_data.export_auditorium_data, login_url='/login_client/'), name='export_auditorium_data_csv'),
    re_path(r'^event_download/export_auditorium_video_data/$', staff_member_required(download_data.export_auditorium_video_data, login_url='/login_client/'), name='export_auditorium_video_data_csv'),
    re_path(r'^event_download/export_auditorium_session_data/$', staff_member_required(download_data.export_auditorium_session_data, login_url='/login_client/'), name='export_auditorium_session_data_csv'),
]
