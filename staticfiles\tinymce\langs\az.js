tinymce.addI18n("az",{"Redo":"\u0130r\u0259li","Undo":"<PERSON><PERSON><PERSON>\u0259","Cut":"K\u0259s","Copy":"K\xf6\xe7\xfcr","Paste":"\u018flav\u0259 et","Select all":"Ham\u0131s\u0131n\u0131 se\xe7","New document":"Yeni s\u0259n\u0259d","Ok":"Oldu","Cancel":"L\u0259\u011fv et","Visual aids":"Konturlar\u0131 g\xf6st\u0259r","Bold":"Qal\u0131n","Italic":"Maili","Underline":"Alt x\u0259ttli","Strikethrough":"Silinmi\u015f","Superscript":"Yuxar\u0131 indeks","Subscript":"A\u015fa\u011f\u0131 indeks","Clear formatting":"Format\u0131 t\u0259mizl\u0259","Remove":"Sil","Align left":"Sol t\u0259r\u0259f \xfczr\u0259","Align center":"M\u0259rk\u0259z \xfczr\u0259","Align right":"Sa\u011f t\u0259r\u0259f \xfczr\u0259","No alignment":"","Justify":"H\u0259r iki t\u0259r\u0259f \xfczr\u0259","Bullet list":"S\u0131ras\u0131z siyah\u0131","Numbered list":"N\xf6mr\u0259l\u0259nmi\u015f siyah\u0131","Decrease indent":"Bo\u015flu\u011fu azalt","Increase indent":"Bo\u015flu\u011fu art\u0131r","Close":"Ba\u011fla","Formats":"Formatlar","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"Sizin brauzeriniz m\xfcbadil\u0259 buferin\u0259 birba\u015fa yolu d\u0259st\u0259kl\u0259mir. Z\u0259hm\u0259t olmasa, bunun yerin\u0259 klaviaturan\u0131n Ctrl+X/C/V d\xfcym\u0259l\u0259rind\u0259n istifad\u0259 edin.","Headings":"Ba\u015fl\u0131qlar","Heading 1":"Ba\u015fl\u0131q 1","Heading 2":"Ba\u015fl\u0131q 2","Heading 3":"Ba\u015fl\u0131q 3","Heading 4":"Ba\u015fl\u0131q 4","Heading 5":"Ba\u015fl\u0131q 5","Heading 6":"Ba\u015fl\u0131q 6","Preformatted":"\u018fvv\u0259lc\u0259d\u0259n formatland\u0131r\u0131lm\u0131\u015f","Div":"","Pre":"","Code":"Kod","Paragraph":"Paraqraf","Blockquote":"Sitat","Inline":"S\u0259tir i\xe7i","Blocks":"Bloklar","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"Hal-haz\u0131rda adi m\u0259tn rejimind\u0259 yerl\u0259\u015fdirilir. M\u0259zmun sad\u0259 m\u0259tn \u015f\u0259klind\u0259 yerl\u0259\u015fdiril\u0259c\u0259k, h\u0259l\u0259 bu se\xe7imi d\u0259yi\u015fdirm\u0259.","Fonts":"Fontlar","Font sizes":"\u015erift \xf6l\xe7\xfcl\u0259ri","Class":"Sinif","Browse for an image":"\u015e\u0259kil se\xe7","OR":"V\u018f YA","Drop an image here":"\u015e\u0259kli buraya s\xfcr\xfckl\u0259yin","Upload":"Y\xfckl\u0259","Uploading image":"","Block":"Blokla","Align":"D\xfczl\u0259ndir","Default":"Susmaya g\xf6r\u0259","Circle":"Dair\u0259","Disc":"Disk","Square":"Sah\u0259","Lower Alpha":"Ki\xe7ik Alfa \u0259lifbas\u0131","Lower Greek":"Ki\xe7ik Yunan \u0259lifbas\u0131","Lower Roman":"Ki\xe7ik Roma \u0259lifbas\u0131","Upper Alpha":"B\xf6y\xfck Alfa \u0259lifbas\u0131","Upper Roman":"B\xf6y\xfck Roma \u0259lifbas\u0131","Anchor...":"Anchor","Anchor":"","Name":"Ad","ID":"","ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.":"","You have unsaved changes are you sure you want to navigate away?":"Sizd\u0259 yadda saxlan\u0131lmayan d\u0259yi\u015fiklikl\u0259r var \u0259minsiniz ki, getm\u0259k ist\u0259yirsiniz?","Restore last draft":"Son layih\u0259nin b\u0259rpas\u0131","Special character...":"X\xfcsusi simvol","Special Character":"X\xfcsusi simvol","Source code":"M\u0259nb\u0259 kodu","Insert/Edit code sample":"Kod n\xfcmun\u0259sin\u0259 \u0259lav\u0259/d\xfcz\u0259li\u015f et","Language":"Dil","Code sample...":"Kod n\xfcmun\u0259si","Left to right":"Soldan sa\u011fa","Right to left":"Sa\u011fdan sola","Title":"Ba\u015fl\u0131q","Fullscreen":"Tam ekran rejimi","Action":"\u018fmr","Shortcut":"Q\u0131sayol","Help":"K\xf6m\u0259k","Address":"Adres","Focus to menubar":"Menyu \xe7ubu\u011funa diqq\u0259t et","Focus to toolbar":"Al\u0259tl\u0259r \xe7ubu\u011funa diqq\u0259t et","Focus to element path":"Elementin m\u0259nb\u0259yin\u0259 diqq\u0259t et","Focus to contextual toolbar":"Kontekst menyuya diqq\u0259t et","Insert link (if link plugin activated)":"Link \u0259lav\u0259 et (\u0259g\u0259r link \u0259lav\u0259si aktivdirs\u0259)","Save (if save plugin activated)":"Yadda\u015fa yaz (\u0259g\u0259r yadda\u015fa yaz \u0259lav\u0259si aktivdirs\u0259)","Find (if searchreplace plugin activated)":"Tap (\u0259g\u0259r axtar\u0131\u015f \u0259lav\u0259si aktivdirs\u0259)","Plugins installed ({0}):":"\u018flav\u0259l\u0259r y\xfckl\u0259ndi ({0}):","Premium plugins:":"Premium \u0259lav\u0259l\u0259r","Learn more...":"Daha \xe7ox \xf6yr\u0259n...","You are using {0}":"Siz {0} istifad\u0259 edirsiniz","Plugins":"\u018flav\u0259l\u0259r","Handy Shortcuts":"Laz\u0131ml\u0131 q\u0131sayollar","Horizontal line":"Horizontal x\u0259tt","Insert/edit image":"\u015e\u0259kilin \u0259lav\u0259/redakt\u0259 edilm\u0259si","Alternative description":"Alternativ t\u0259svir","Accessibility":"\u018fl\xe7atanl\u0131q","Image is decorative":"","Source":"M\u0259nb\u0259","Dimensions":"\xd6l\xe7\xfcl\u0259r","Constrain proportions":"Nisb\u0259tl\u0259rin saxlan\u0131lmas\u0131","General":"\xdcmumi","Advanced":"Geni\u015fl\u0259nmi\u015f","Style":"Stil","Vertical space":"Vertikal sah\u0259","Horizontal space":"Horizontal sah\u0259","Border":"\xc7\u0259r\xe7iv\u0259","Insert image":"\u015e\u0259kil yerl\u0259\u015fdir","Image...":"\u015e\u0259kil","Image list":"\u015e\u0259kil listi","Resize":"\xd6l\xe7\xfcl\u0259ri d\u0259yi\u015f","Insert date/time":"G\xfcn/tarix \u0259lav\u0259 et","Date/time":"Tarix/saat","Insert/edit link":"Linkin \u0259lav\u0259/redakt\u0259 edilm\u0259si","Text to display":"G\xf6r\xfcn\u0259n yaz\u0131n\u0131n t\u0259sviri","Url":"Linkin \xfcnvan\u0131","Open link in...":"Ba\u011flant\u0131y\u0131 a\xe7\u0131n","Current window":"Cari p\u0259nc\u0259r\u0259","None":"Yoxdur","New window":"Yeni p\u0259nc\u0259r\u0259d\u0259 a\xe7\u0131ls\u0131n","Open link":"Ke\xe7idi a\xe7","Remove link":"Linki sil","Anchors":"L\xf6vb\u0259rl\u0259r","Link...":"Ba\u011flant\u0131","Paste or type a link":"Ke\xe7idi yerl\u0259\u015fdirin v\u0259 ya yaz\u0131n","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":"Daxil etdiyiniz URL bir e-mail kimi g\xf6r\xfcn\xfcr. \u018fg\u0259r t\u0259l\u0259b olunan mailto: prefix \u0259lav\u0259 etm\u0259k ist\u0259yirsiniz?","The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":"Daxil etdiyiniz URL bir e-mail kimi g\xf6r\xfcn\xfcr. \u018fg\u0259r t\u0259l\u0259b olunan mailto: prefix \u0259lav\u0259 etm\u0259k ist\u0259yirsiniz?","The URL you entered seems to be an external link. Do you want to add the required https:// prefix?":"","Link list":"Ke\xe7id listi","Insert video":"Videonun \u0259lav\u0259 edilm\u0259si","Insert/edit video":"Videonun \u0259lav\u0259/redakt\u0259 edilm\u0259si","Insert/edit media":"Media \u0259lav\u0259/d\xfcz\u0259li\u015f et","Alternative source":"Alternativ m\u0259nb\u0259","Alternative source URL":"Alternativ m\u0259nb\u0259 URL-i","Media poster (Image URL)":"Media afi\u015fas\u0131 (\u015e\u0259kil URL)","Paste your embed code below:":"\xd6z kodunuzu a\u015fa\u011f\u0131 \u0259lav\u0259 edin:","Embed":"\u018flav\u0259 etm\u0259k \xfc\xe7\xfcn kod","Media...":"Media","Nonbreaking space":"Q\u0131r\u0131lmaz sah\u0259","Page break":"S\u0259hif\u0259nin q\u0131r\u0131lmas\u0131","Paste as text":"M\u0259tn kimi \u0259lav\u0259 et","Preview":"\u0130lkinbax\u0131\u015f","Print":"\xc7ap","Print...":"\xc7ap et","Save":"Yadda saxla","Find":"Tap","Replace with":"Bununla d\u0259yi\u015fdir","Replace":"D\u0259yi\u015fdir","Replace all":"Ham\u0131s\u0131n\u0131 d\u0259yi\u015fdir","Previous":"\u018fvv\u0259lki","Next":"N\xf6vb\u0259ti","Find and Replace":"Tap v\u0259 d\u0259yi\u015fdir","Find and replace...":"Tap\u0131n v\u0259 d\u0259yi\u015fdirin","Could not find the specified string.":"G\xf6st\u0259ril\u0259n s\u0259tiri tapmaq olmur","Match case":"Registri n\u0259z\u0259r\u0259 al","Find whole words only":"Yaln\u0131z b\xfct\xf6v s\xf6zl\u0259ri tap\u0131n","Find in selection":"Se\xe7imd\u0259 tap","Insert table":"S\u0259tir \u0259lav\u0259 et","Table properties":"C\u0259dv\u0259lin x\xfcsusiyy\u0259tl\u0259ri","Delete table":"C\u0259dv\u0259li sil","Cell":"H\xfccr\u0259","Row":"S\u0259tir","Column":"S\xfctun","Cell properties":"H\xfccr\u0259nin x\xfcsusiyy\u0259tl\u0259ri","Merge cells":"H\xfccr\u0259l\u0259ri birl\u0259\u015ftir","Split cell":"H\xfccr\u0259l\u0259rin say\u0131","Insert row before":"\u018fvv\u0259lin\u0259 s\u0259tir \u0259lav\u0259 et","Insert row after":"Sonras\u0131na s\u0259tir \u0259lav\u0259 et","Delete row":"S\u0259tri sil","Row properties":"S\u0259trin x\xfcsusiyy\u0259tl\u0259ri","Cut row":"S\u0259tiri k\u0259s","Cut column":"S\xfctunu k\u0259s","Copy row":"S\u0259tiri k\xf6\xe7\xfcr","Copy column":"S\xfctunu kopyala","Paste row before":"\u018fvv\u0259lin\u0259 s\u0259tir \u0259lav\u0259 et","Paste column before":"S\xfctundan \u0259vv\u0259l yap\u0131\u015fd\u0131r","Paste row after":"Sonras\u0131na s\u0259tir \u0259lav\u0259 et","Paste column after":"S\xfctundan sonra yap\u0131\u015fd\u0131r","Insert column before":"\u018fvv\u0259lin\u0259 s\u0259tir \u0259lav\u0259 et","Insert column after":"\u018fvv\u0259lin\u0259 s\xfctun \u0259lav\u0259 et","Delete column":"S\xfctunu sil","Cols":"S\xfctunlar","Rows":"S\u0259tirl\u0259r","Width":"Eni","Height":"H\xfcnd\xfcrl\xfcy\xfc","Cell spacing":"H\xfccr\u0259l\u0259rin aras\u0131nda m\u0259saf\u0259","Cell padding":"H\xfccr\u0259l\u0259rin sah\u0259l\u0259ri","Row clipboard actions":"S\u0259tir m\xfcbadil\u0259 buferi h\u0259r\u0259k\u0259tl\u0259ri","Column clipboard actions":"S\xfctun m\xfcbadil\u0259 buferi h\u0259r\u0259k\u0259tl\u0259ri","Table styles":"C\u0259dv\u0259l still\u0259ri","Cell styles":"Xana still\u0259ri","Column header":"S\xfctun ba\u015fl\u0131\u011f\u0131","Row header":"S\u0259tir ba\u015fl\u0131\u011f\u0131","Table caption":"C\u0259dv\u0259l ba\u015fl\u0131\u011f\u0131","Caption":"Ba\u015flan\u011f\u0131c","Show caption":"Ba\u015fl\u0131\u011f\u0131 g\xf6st\u0259r","Left":"Sol t\u0259r\u0259f \xfczr\u0259","Center":"M\u0259rk\u0259z \xfczr\u0259","Right":"Sa\u011f t\u0259r\u0259f \xfczr\u0259","Cell type":"H\xfccr\u0259nin tipi","Scope":"Sfera","Alignment":"D\xfczl\u0259ndirm\u0259","Horizontal align":"\xdcf\xfcqi d\xfczl\u0259ndirm\u0259","Vertical align":"\u015eaquli d\xfczl\u0259ndirm\u0259","Top":"Yuxar\u0131","Middle":"Orta","Bottom":"A\u015fa\u011f\u0131","Header cell":"H\xfccr\u0259nin ba\u015fl\u0131\u011f\u0131","Row group":"S\u0259tirin qrupu","Column group":"S\xfctunun qrupu","Row type":"S\u0259tirin tipi","Header":"Ba\u015fl\u0131q","Body":"K\xfctl\u0259","Footer":"\u018fn a\u015fa\u011f\u0131","Border color":"\xc7\u0259r\xe7iv\u0259 r\u0259ngi","Solid":"","Dotted":"","Dashed":"","Double":"","Groove":"","Ridge":"","Inset":"","Outset":"","Hidden":"Gizli","Insert template...":"\u015eablon daxil edin","Templates":"\u015eablonlar","Template":"\u015eablon","Insert Template":"\u015eablon daxil et","Text color":"M\u0259tnin r\u0259ngi","Background color":"Arxafon r\u0259ngi","Custom...":"\xc7\u0259kilm\u0259...","Custom color":"\xc7\u0259kilm\u0259 r\u0259ng","No color":"R\u0259ngsiz","Remove color":"R\u0259ngi silin","Show blocks":"Bloklar\u0131 g\xf6st\u0259r","Show invisible characters":"G\xf6r\xfcnm\u0259y\u0259n simvollar\u0131 g\xf6st\u0259r","Word count":"S\xf6z say\u0131","Count":"Say","Document":"S\u0259n\u0259d","Selection":"Se\xe7im","Words":"S\xf6zl\u0259r","Words: {0}":"S\xf6zl\u0259r: {0}","{0} words":"{0} s\xf6z","File":"Fayl","Edit":"Redakt\u0259 et","Insert":"\u018flav\u0259 et","View":"G\xf6r\xfcn\xfc\u015f","Format":"Format","Table":"C\u0259dv\u0259l","Tools":"Al\u0259tl\u0259r","Powered by {0}":"{0} t\u0259r\u0259find\u0259n t\u0259chiz edilib","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"B\xf6y\xfck m\u0259tn sah\u0259si \u0259lav\u0259 edilib. Menyu \xfc\xe7\xfcn ALT-F9 d\xfcym\u0259sini bas\u0131n. Al\u0259tl\u0259r paneli \xfc\xe7\xfcn ALT-F10 d\xfcym\u0259sini bas\u0131n. K\xf6m\u0259k \xfc\xe7\xfcn ALT-0 d\xfcym\u0259l\u0259rin bas\u0131n.","Image title":"\u015e\u0259kil ba\u015fl\u0131\u011f\u0131","Border width":"K\u0259narl\u0131q geni\u015fliyi","Border style":"K\u0259narl\u0131q stili","Error":"X\u0259ta","Warn":"X\u0259b\u0259rdar et","Valid":"Etibarl\u0131d\u0131r","To open the popup, press Shift+Enter":"A\xe7\u0131lan p\u0259nc\u0259r\u0259ni a\xe7maq \xfc\xe7\xfcn Shift + Enter d\xfcym\u0259l\u0259rini bas\u0131n","Rich Text Area":"Z\u0259ngin m\u0259tn redaktoru","Rich Text Area. Press ALT-0 for help.":"Z\u0259ngin M\u0259tn Sah\u0259si. Yard\u0131m \xfc\xe7\xfcn ALT-0 d\xfcym\u0259sin\u0259 bas\u0131n.","System Font":"Sistem Fontu","Failed to upload image: {0}":"\u015e\u0259kil y\xfckl\u0259nm\u0259di: {0}","Failed to load plugin: {0} from url {1}":"Qo\u015fma y\xfckl\u0259nm\u0259di: {0} urldan {1}","Failed to load plugin url: {0}":"Qo\u015fma url-i y\xfckl\u0259nm\u0259di: {0}","Failed to initialize plugin: {0}":"Qo\u015fman\u0131 i\u015f\u0259 salmaq al\u0131nmad\u0131: {0}","example":"n\xfcmun\u0259","Search":"Axtar","All":"Ham\u0131s\u0131","Currency":"Valyuta","Text":"M\u0259tn","Quotations":"T\u0259klifl\u0259r","Mathematical":"Riyazi","Extended Latin":"Geni\u015fl\u0259ndirilmi\u015f Lat\u0131n","Symbols":"Simvollar","Arrows":"Oxlar","User Defined":"M\xfc\u0259yy\u0259n edilmi\u015f istifad\u0259\xe7i","dollar sign":"dollar i\u015far\u0259si","currency sign":"valyuta i\u015far\u0259si","euro-currency sign":"avro-valyuta simvolu","colon sign":"qo\u015fa n\xf6qt\u0259","cruzeiro sign":"","french franc sign":"frans\u0131z frank\u0131 i\u015far\u0259si","lira sign":"lir\u0259 simvolu","mill sign":"mil simvolu","naira sign":"nayra simvolu","peseta sign":"peseta simvolu","rupee sign":"rupi simvolu","won sign":"von simvolu","new sheqel sign":"yeni \u015fekel simvolu","dong sign":"donq simvolu","kip sign":"kip simvolu","tugrik sign":"tuqrik simvolu","drachma sign":"draxma simolu","german penny symbol":"alman funt sterlinqi simvolu","peso sign":"peso simvolu","guarani sign":"guarani simvolu","austral sign":"avstral simvolu","hryvnia sign":"qrivna simvolu","cedi sign":"sedi simvolu","livre tournois sign":"tur livri simvolu","spesmilo sign":"spesmilo simvolu","tenge sign":"tenqe simvolu","indian rupee sign":"hindistan rupisi simvolu","turkish lira sign":"t\xfcrkiy\u0259 lir\u0259si simvolu","nordic mark sign":"skandinav mark\u0131 simvolu","manat sign":"manat simvolu","ruble sign":"rubl simvolu","yen character":"ye simvolu","yuan character":"yuan simvolu","yuan character, in hong kong and taiwan":"yuan simvolu, Hong Kong v\u0259 Tayvanda","yen/yuan character variant one":"yen/yuan simvolu variant\u0131","Emojis":"Emoji","Emojis...":"Emojil\u0259r...","Loading emojis...":"Emojil\u0259r y\xfckl\u0259nir...","Could not load emojis":"Emoji y\xfckl\u0259m\u0259k m\xfcmk\xfcn olmad\u0131","People":"\u0130nsanlar","Animals and Nature":"Heyvanlar v\u0259 t\u0259bi\u0259t","Food and Drink":"Yem\u0259k v\u0259 i\xe7ki","Activity":"F\u0259aliyy\u0259t","Travel and Places":"S\u0259yah\u0259t v\u0259 m\u0259kanlar","Objects":"\u018f\u015fyalar","Flags":"Bayraqlar","Characters":"Simvollar","Characters (no spaces)":"Simvollar (bo\u015fluqsuz)","{0} characters":"{0} simvol","Error: Form submit field collision.":"X\u0259ta: Forma g\xf6nd\u0259rm\u0259 sah\u0259sind\u0259 toqqu\u015fma.","Error: No form element found.":"X\u0259ta: He\xe7 bir forma elementi tap\u0131lmad\u0131.","Color swatch":"R\u0259ng n\xfcmun\u0259si","Color Picker":"R\u0259ng se\xe7ici","Invalid hex color code: {0}":"Yanl\u0131\u015f hex r\u0259ng kodu: {0}","Invalid input":"Yaln\u0131\u015f daxiletm\u0259","R":"R","Red component":"Q\u0131rm\u0131z\u0131 komponent","G":"G","Green component":"Ya\u015f\u0131l komponent","B":"B","Blue component":"G\xf6y komponent","#":"#","Hex color code":"Hex r\u0259ng kodu","Range 0 to 255":"0-dan 255-\u0259 q\u0259d\u0259r diapazon","Turquoise":"Firuz\u0259yi","Green":"Ya\u015f\u0131l","Blue":"Mavi","Purple":"B\u0259n\xf6v\u015f\u0259yi","Navy Blue":"T\xfcnd g\xf6y","Dark Turquoise":"T\xfcnd firuz\u0259yi","Dark Green":"T\xfcnd ya\u015f\u0131l","Medium Blue":"","Medium Purple":"","Midnight Blue":"Gec\u0259 mavisi","Yellow":"Sar\u0131","Orange":"Nar\u0131nc\u0131","Red":"Q\u0131rm\u0131z\u0131","Light Gray":"A\xe7\u0131q boz","Gray":"Boz","Dark Yellow":"T\xfcnd sar\u0131","Dark Orange":"T\xfcnd nar\u0131nc\u0131","Dark Red":"T\xfcnd q\u0131rm\u0131z\u0131","Medium Gray":"","Dark Gray":"T\xfcnd boz","Light Green":"A\xe7\u0131q ya\u015f\u0131l","Light Yellow":"A\xe7\u0131q sar\u0131","Light Red":"A\xe7\u0131q q\u0131rm\u0131z\u0131","Light Purple":"A\xe7\u0131q b\u0259n\xf6v\u015f\u0259yi","Light Blue":"A\xe7\u0131q mavi","Dark Purple":"T\xfcnd b\u0259n\xf6v\u015f\u0259yi","Dark Blue":"T\xfcnd g\xf6y","Black":"Qara","White":"A\u011f","Switch to or from fullscreen mode":"Tam ekran rejimin ke\xe7in v\u0259 ya \xe7\u0131x\u0131n","Open help dialog":"K\xf6m\u0259k p\u0259nc\u0259r\u0259sini a\xe7","history":"tarix\xe7\u0259","styles":"still\u0259r","formatting":"formatla\u015fd\u0131rma","alignment":"d\xfczl\u0259ndirm\u0259","indentation":"girinti","Font":"\u015erift","Size":"\xd6l\xe7\xfc","More...":"Daha \xe7ox...","Select...":"Se\xe7...","Preferences":"T\u0259nziml\u0259m\u0259l\u0259r","Yes":"B\u0259li","No":"Xeyr","Keyboard Navigation":"Klaviatura naviqasiyas\u0131","Version":"Versiya","Code view":"Kod g\xf6r\xfcn\xfc\u015f\xfc","Open popup menu for split buttons":"B\xf6lm\u0259 d\xfcym\u0259l\u0259ri \xfc\xe7\xfcn a\xe7\u0131lan menyunu a\xe7\u0131n","List Properties":"Siyah\u0131 x\xfcsusiyy\u0259tl\u0259ri","List properties...":"Siyah\u0131 x\xfcsusiyy\u0259tl\u0259ri...","Start list at number":"","Line height":"S\u0259tir h\xfcnd\xfcrl\xfcy\xfc","Dropped file type is not supported":"Se\xe7ilmi\u015f fayl n\xf6v\xfc d\u0259st\u0259kl\u0259nmir","Loading...":"Y\xfckl\u0259nir...","ImageProxy HTTP error: Rejected request":"ImageProxy HTTP x\u0259tas\u0131: R\u0259dd edilmi\u015f sor\u011fu","ImageProxy HTTP error: Could not find Image Proxy":"ImageProxy HTTP x\u0259tas\u0131: \u015e\u0259kil Proksisini tapmaq m\xfcmk\xfcn olmad\u0131","ImageProxy HTTP error: Incorrect Image Proxy URL":"ImageProxy HTTP x\u0259tas\u0131: Yanl\u0131\u015f \u015e\u0259kil Proksi URL-i","ImageProxy HTTP error: Unknown ImageProxy error":"ImageProxy HTTP x\u0259tas\u0131: Nam\u0259lum ImageProxy x\u0259tas\u0131"});