# Generated by Django 3.0.9 on 2021-01-04 20:56

from django.db import migrations, models
import django_tenants.postgresql_backend.base


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('domain_url', models.CharField(max_length=128, unique=True)),
                ('schema_name', models.CharField(max_length=63, unique=True, validators=[django_tenants.postgresql_backend.base._check_schema_name])),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
