# Static Files Configuration for Django 4 on Amazon Linux 2023
# This configuration ensures proper static file serving via nginx

option_settings:
  # Static file mapping for nginx proxy
  aws:elasticbeanstalk:environment:proxy:staticfiles:
    # Map /static URL path to staticfiles directory (where collectstatic puts files)
    /static: staticfiles
    # Optional: Add media files mapping if needed
    # /media: media

  # Ensure proper environment variables for static files
  aws:elasticbeanstalk:application:environment:
    # Django static files settings
    DJANGO_SETTINGS_MODULE: "backend.settings"
    # Disable WhiteNoise in favor of nginx static file serving
    DISABLE_WHITENOISE: "true"

# Container commands to ensure static files are properly collected
container_commands:
  # Ensure staticfiles directory exists and has proper permissions
  01_create_staticfiles_dir:
    command: "mkdir -p staticfiles && chmod 755 staticfiles"
    ignoreErrors: true

  # Collect static files before deployment
  02_collectstatic:
    command: "source /var/app/venv/*/bin/activate && python manage.py collectstatic --noinput --clear"
    leader_only: true
    ignoreErrors: false

  # Verify static files were collected
  03_verify_static_files:
    command: "ls -la staticfiles/ | head -10"
    ignoreErrors: true

  # Set proper permissions on static files
  04_set_static_permissions:
    command: "find staticfiles -type f -exec chmod 644 {} \\; && find staticfiles -type d -exec chmod 755 {} \\;"
    ignoreErrors: true
