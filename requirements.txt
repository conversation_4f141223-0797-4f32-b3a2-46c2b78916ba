# ================================================================================
# REQUIREMENTS.TXT - Unified for Development and Production
# Compatible with Python 3.13 and Django 4.2.23 LTS
# ================================================================================

# Build dependencies (required for compilation)
pycparser>=2.21
cffi>=1.17.1
setuptools>=69.0.0
wheel>=0.42.0
Cython>=3.0.10

# ================================================================================
# CORE DJANGO FRAMEWORK
# ================================================================================
Django>=4.2.11,<5.0
djangorestframework>=3.14.0
asgiref>=3.7.2

# ================================================================================
# DATABASE & MULTI-TENANCY
# ================================================================================
psycopg2-binary>=2.9.9
django-tenants>=3.6.1

# ================================================================================
# DJANGO EXTENSIONS & UTILITIES
# ================================================================================
django-cors-headers>=4.3.1
django-environ>=0.11.2
django-extra-views>=0.15.0
django-model-utils>=4.3.1
django-postgres-metrics>=0.11.0
django-storages>=1.14.2
django-tinymce>=4.0.0
django-user-agents>=0.4.0
django-tables2>=2.7.0
django-debug-toolbar>=4.3.0

# ================================================================================
# REST FRAMEWORK & API
# ================================================================================
djangorestframework-api-key>=3.0.0
djangorestframework-simplejwt>=5.3.1

# ================================================================================
# ASYNC & REAL-TIME FEATURES
# ================================================================================
channels>=4.0.0
channels-redis>=4.1.0
redis>=5.0.2
hiredis>=2.3.2
daphne>=4.1.0
aioredis>=2.0.1

# ================================================================================
# AWS & CLOUD SERVICES
# ================================================================================
boto3>=1.34.0
botocore>=1.34.0
s3transfer>=0.13.0

# ================================================================================
# WEB SERVER & STATIC FILES
# ================================================================================
whitenoise>=6.6.0
gunicorn>=21.2.0

# ================================================================================
# SECURITY & AUTHENTICATION
# ================================================================================
cryptography>=42.0.5
PyJWT>=2.8.0
pyOpenSSL>=24.0.0

# ================================================================================
# NETWORKING & HTTP
# ================================================================================
requests>=2.31.0
urllib3>=2.2.1
certifi>=2024.2.2
charset-normalizer>=3.3.2
idna>=3.6

# ================================================================================
# DATA PROCESSING & UTILITIES
# ================================================================================
numpy>=1.26.4
pandas>=2.2.1
Pillow>=10.4.0
PyPDF2>=3.0.1
simplejson>=3.19.2

# ================================================================================
# GOOGLE SERVICES & ANALYTICS
# ================================================================================
google-analytics-data>=0.17.2
google-api-core>=2.17.1
google-api-python-client>=2.120.0
google-auth>=2.28.1
google-auth-httplib2>=0.2.0
googleapis-common-protos>=1.62.0

# ================================================================================
# ASYNC NETWORKING
# ================================================================================
aiohttp>=3.9.5
aiodns>=3.1.0
aiofile>=3.7.1
aiosignal>=1.3.1
async-timeout>=4.0.3

# ================================================================================
# TASK QUEUE & BACKGROUND JOBS
# ================================================================================
huey>=2.5.0

# ================================================================================
# MONITORING & LOGGING
# ================================================================================
watchtower>=3.0.1

# ================================================================================
# THIRD-PARTY INTEGRATIONS
# ================================================================================
stream-chat>=4.5.1
mux-python>=3.3.1
oauth2client>=4.1.3

# ================================================================================
# TESTING
# ================================================================================
pytest>=8.0.2

# ================================================================================
# UTILITY LIBRARIES - Updated grpcio for Python 3.13 compatibility
# ================================================================================
attrs>=23.2.0
autobahn>=23.1.2
Automat>=22.10.0
bleach>=6.1.0
cachetools>=5.3.2
caio>=0.9.3
constantly>=15.1.0
Deprecated>=1.2.14
frozenlist>=1.4.1
grpcio>=1.73.1
grpcio-status>=1.73.1
httplib2>=0.22.0
hyperlink>=21.0.0
importlib-metadata>=7.0.1
incremental>=22.10.0
iniconfig>=2.0.0
itypes>=1.2.0
Jinja2>=3.1.3
jmespath>=1.0.1
MarkupSafe>=2.1.5
msgpack>=1.0.7
multidict>=6.0.5
ordered-set>=4.1.0
packaging>=23.2
pluggy>=1.4.0
proto-plus>=1.23.0
protobuf>=4.25.3
pyasn1>=0.5.1
pyasn1-modules>=0.3.0
pycares>=4.4.0
pycryptodome>=3.20.0
PyHamcrest>=2.1.0
pyparsing>=3.1.1
python-dateutil>=2.8.2
pytz>=2024.1
PyYAML>=6.0.1
rsa>=4.9
service-identity>=24.1.0
six>=1.16.0
sqlparse>=0.4.4
toml>=0.10.2
tomli>=2.0.1
Twisted>=24.2.0
txaio>=23.1.1
typing-extensions>=4.10.0
uritemplate>=4.1.1
webencodings>=0.5.1
wrapt>=1.16.0
yarl>=1.9.4
zipp>=3.17.0
zope.interface>=6.2
chardet>=5.2.0
