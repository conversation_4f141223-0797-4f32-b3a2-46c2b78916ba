tinymce.addI18n('he_IL',{
"Redo": "\u05d1\u05e6\u05e2 \u05e9\u05d5\u05d1",
"Undo": "\u05d1\u05d8\u05dc",
"Cut": "\u05d2\u05d6\u05d5\u05e8",
"Copy": "\u05d4\u05e2\u05ea\u05e7",
"Paste": "\u05d4\u05d3\u05d1\u05e7",
"Select all": "\u05d1\u05d7\u05e8 \u05d4\u05db\u05dc",
"New document": "\u05de\u05e1\u05de\u05da \u05d7\u05d3\u05e9",
"Ok": "\u05d0\u05d9\u05e9\u05d5\u05e8",
"Cancel": "\u05d1\u05d9\u05d8\u05d5\u05dc",
"Visual aids": "\u05e2\u05d6\u05e8\u05d9\u05dd \u05d7\u05d6\u05d5\u05ea\u05d9\u05d9\u05dd",
"Bold": "\u05de\u05d5\u05d3\u05d2\u05e9",
"Italic": "\u05e0\u05d8\u05d5\u05d9",
"Underline": "\u05e7\u05d5 \u05ea\u05d7\u05ea\u05d5\u05df",
"Strikethrough": "\u05e7\u05d5 \u05d7\u05d5\u05e6\u05d4",
"Superscript": "\u05db\u05ea\u05d1 \u05e2\u05d9\u05dc\u05d9",
"Subscript": "\u05db\u05ea\u05d1 \u05ea\u05d7\u05ea\u05d9",
"Clear formatting": "\u05e0\u05e7\u05d4 \u05e2\u05d9\u05e6\u05d5\u05d1",
"Align left": "\u05d9\u05e9\u05e8 \u05dc\u05e9\u05de\u05d0\u05dc",
"Align center": "\u05de\u05e8\u05db\u05d6",
"Align right": "\u05d9\u05e9\u05e8 \u05dc\u05d9\u05de\u05d9\u05df",
"Justify": "\u05d9\u05d9\u05e9\u05e8",
"Bullet list": "\u05e8\u05e9\u05d9\u05de\u05ea \u05ea\u05d1\u05dc\u05d9\u05d8\u05d9\u05dd",
"Numbered list": "\u05e8\u05e9\u05d9\u05de\u05d4 \u05de\u05de\u05d5\u05e1\u05e4\u05e8\u05ea",
"Decrease indent": "\u05d4\u05e7\u05d8\u05df \u05d4\u05d6\u05d7\u05d4",
"Increase indent": "\u05d4\u05d2\u05d3\u05dc \u05d4\u05d6\u05d7\u05d4",
"Close": "\u05e1\u05d2\u05d5\u05e8",
"Formats": "\u05e2\u05d9\u05e6\u05d5\u05d1\u05d9\u05dd",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\u05d4\u05d3\u05e4\u05d3\u05e4\u05df \u05e9\u05dc\u05da \u05d0\u05d9\u05e0\u05d5 \u05de\u05d0\u05e4\u05e9\u05e8 \u05d2\u05d9\u05e9\u05d4 \u05d9\u05e9\u05d9\u05e8\u05d4 \u05dc\u05dc\u05d5\u05d7. \u05d0\u05e0\u05d0 \u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05e7\u05d9\u05e6\u05d5\u05e8\u05d9 \u05d4\u05de\u05e7\u05dc\u05d3\u05ea Ctrl+X\/C\/V \u05d1\u05de\u05e7\u05d5\u05dd.",
"Headers": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea",
"Header 1": "\u05db\u05d5\u05ea\u05e8\u05ea 1",
"Header 2": "\u05db\u05d5\u05ea\u05e8\u05ea 2",
"Header 3": "\u05db\u05d5\u05ea\u05e8\u05ea 3",
"Header 4": "\u05db\u05d5\u05ea\u05e8\u05ea 4",
"Header 5": "\u05db\u05d5\u05ea\u05e8\u05ea 5",
"Header 6": "\u05db\u05d5\u05ea\u05e8\u05ea 6",
"Headings": "\u05db\u05d5\u05ea\u05e8\u05d5\u05ea",
"Heading 1": "\u05db\u05d5\u05ea\u05e8\u05ea 1",
"Heading 2": "\u05db\u05d5\u05ea\u05e8\u05ea 2",
"Heading 3": "\u05db\u05d5\u05ea\u05e8\u05ea 3",
"Heading 4": "\u05db\u05d5\u05ea\u05e8\u05ea 4",
"Heading 5": "\u05db\u05d5\u05ea\u05e8\u05ea 5",
"Heading 6": "\u05db\u05d5\u05ea\u05e8\u05ea 6",
"Preformatted": "\u05de\u05e2\u05d5\u05e6\u05d1 \u05de\u05e8\u05d0\u05e9",
"Div": "Div",
"Pre": "Pre",
"Code": "\u05e7\u05d5\u05d3",
"Paragraph": "\u05e4\u05e1\u05e7\u05d4",
"Blockquote": "Blockquote",
"Inline": "\u05d1\u05ea\u05d5\u05da \u05e9\u05d5\u05e8\u05d4",
"Blocks": "\u05d1\u05dc\u05d5\u05e7\u05d9\u05dd",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\u05d4\u05d3\u05d1\u05e7\u05d4 \u05d1\u05de\u05e6\u05d1 \u05d8\u05e7\u05e1\u05d8 \u05e8\u05d2\u05d9\u05dc. \u05ea\u05db\u05e0\u05d9\u05dd \u05d9\u05d5\u05d3\u05d1\u05e7\u05d5 \u05de\u05e2\u05ea\u05d4 \u05db\u05d8\u05e7\u05e1\u05d8 \u05e8\u05d2\u05d9\u05dc \u05e2\u05d3 \u05e9\u05ea\u05db\u05d1\u05d4 \u05d0\u05e4\u05e9\u05e8\u05d5\u05ea \u05d6\u05d5.",
"Fonts": "\u05d2\u05d5\u05e4\u05e0\u05d9\u05dd",
"Font Sizes": "\u05d2\u05d5\u05d3\u05dc \u05d2\u05d5\u05e4\u05e0\u05d9\u05dd",
"Class": "\u05de\u05d7\u05dc\u05e7\u05d4",
"Browse for an image": "\u05d7\u05e4\u05e9 \u05ea\u05de\u05d5\u05e0\u05d4",
"OR": "\u05d0\u05d5",
"Drop an image here": "\u05e9\u05d7\u05e8\u05e8 \u05ea\u05de\u05d5\u05e0\u05d4 \u05db\u05d0\u05df",
"Upload": "\u05d4\u05e2\u05dc\u05d4",
"Block": "\u05d1\u05dc\u05d5\u05e7",
"Align": "\u05d9\u05e9\u05e8",
"Default": "\u05d1\u05e8\u05d9\u05e8\u05ea \u05de\u05d7\u05d3\u05dc",
"Circle": "\u05e2\u05d9\u05d2\u05d5\u05dc",
"Disc": "\u05d7\u05d9\u05e9\u05d5\u05e7",
"Square": "\u05e8\u05d9\u05d1\u05d5\u05e2",
"Lower Alpha": "\u05d0\u05d5\u05ea\u05d9\u05d5\u05ea \u05d0\u05e0\u05d2\u05dc\u05d9\u05d5\u05ea \u05e7\u05d8\u05e0\u05d5\u05ea",
"Lower Greek": "\u05d0\u05d5\u05ea\u05d9\u05d5\u05ea \u05d9\u05d5\u05d5\u05e0\u05d9\u05d5\u05ea \u05e7\u05d8\u05e0\u05d5\u05ea",
"Lower Roman": "\u05e1\u05e4\u05e8\u05d5\u05ea \u05e8\u05d5\u05de\u05d9\u05d5\u05ea \u05e7\u05d8\u05e0\u05d5\u05ea",
"Upper Alpha": "\u05d0\u05d5\u05ea\u05d9\u05d5\u05ea \u05d0\u05e0\u05d2\u05dc\u05d9\u05d5\u05ea \u05d2\u05d3\u05d5\u05dc\u05d5\u05ea",
"Upper Roman": "\u05e1\u05e4\u05e8\u05d5\u05ea \u05e8\u05d5\u05de\u05d9\u05d5\u05ea \u05d2\u05d3\u05d5\u05dc\u05d5\u05ea",
"Anchor...": "\u05e2\u05d5\u05d2\u05df...",
"Name": "\u05e9\u05dd",
"Id": "\u05de\u05d6\u05d4\u05d4",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "\u05d4\u05de\u05d6\u05d4\u05d4 \u05d7\u05d9\u05d9\u05d1 \u05dc\u05d4\u05ea\u05d7\u05d9\u05dc \u05d1\u05d0\u05d5\u05ea \u05d5\u05dc\u05d0\u05d7\u05e8\u05d9\u05d4 \u05e8\u05e7 \u05d0\u05d5\u05ea\u05d9\u05d5\u05ea, \u05de\u05e1\u05e4\u05e8\u05d9\u05dd, \u05de\u05e7\u05e4\u05d9\u05dd, \u05e0\u05e7\u05d5\u05d3\u05d5\u05ea, \u05e0\u05e7\u05d5\u05d3\u05ea\u05d9\u05d9\u05dd \u05d0\u05d5 \u05e7\u05d5\u05d5\u05d9\u05dd \u05ea\u05d7\u05ea\u05d9\u05d9\u05dd.",
"You have unsaved changes are you sure you want to navigate away?": "\u05d4\u05e9\u05d9\u05e0\u05d5\u05d9\u05d9\u05dd \u05dc\u05d0 \u05e0\u05e9\u05de\u05e8\u05d5. \u05d1\u05d8\u05d5\u05d7 \u05e9\u05d1\u05e8\u05e6\u05d5\u05e0\u05da \u05dc\u05e6\u05d0\u05ea \u05de\u05d4\u05d3\u05e3?",
"Restore last draft": "\u05e9\u05d7\u05d6\u05e8 \u05d8\u05d9\u05d5\u05d8\u05d4 \u05d0\u05d7\u05e8\u05d5\u05e0\u05d4",
"Special characters...": "\u05ea\u05d5\u05d5\u05d9\u05dd \u05de\u05d9\u05d5\u05d7\u05d3\u05d9\u05dd...",
"Source code": "\u05e7\u05d5\u05d3 \u05de\u05e7\u05d5\u05e8",
"Insert\/Edit code sample": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05d3\u05d5\u05d2\u05de\u05ea \u05e7\u05d5\u05d3",
"Language": "\u05e9\u05e4\u05d4",
"Code sample...": "\u05d3\u05d5\u05d2\u05de\u05ea \u05e7\u05d5\u05d3...",
"Color Picker": "\u05d1\u05d5\u05e8\u05e8 \u05e6\u05d1\u05e2\u05d9\u05dd",
"R": "\u05d0'",
"G": "\u05d9'",
"B": "\u05db'",
"Left to right": "\u05de\u05e9\u05de\u05d0\u05dc \u05dc\u05d9\u05de\u05d9\u05df",
"Right to left": "\u05de\u05d9\u05de\u05d9\u05df \u05dc\u05e9\u05de\u05d0\u05dc",
"Emoticons...": "\u05e1\u05de\u05dc\u05d9 \u05d4\u05d1\u05e2\u05d4...",
"Metadata and Document Properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05de\u05d8\u05d4-\u05e0\u05ea\u05d5\u05e0\u05d9\u05dd \u05d5\u05de\u05e1\u05de\u05da",
"Title": "\u05db\u05d5\u05ea\u05e8\u05ea",
"Keywords": "\u05de\u05d9\u05dc\u05d5\u05ea \u05de\u05e4\u05ea\u05d7",
"Description": "\u05ea\u05d9\u05d0\u05d5\u05e8",
"Robots": "\u05e8\u05d5\u05d1\u05d5\u05d8\u05d9\u05dd",
"Author": "\u05de\u05d7\u05d1\u05e8",
"Encoding": "\u05e7\u05d9\u05d3\u05d5\u05d3",
"Fullscreen": "\u05de\u05e1\u05da \u05de\u05dc\u05d0",
"Action": "\u05e4\u05e2\u05d5\u05dc\u05d4",
"Shortcut": "\u05e7\u05d9\u05e6\u05d5\u05e8",
"Help": "\u05e2\u05d6\u05e8\u05d4",
"Address": "\u05db\u05ea\u05d5\u05d1\u05ea",
"Focus to menubar": "\u05d4\u05e2\u05d1\u05e8 \u05de\u05d9\u05e7\u05d5\u05d3 \u05dc\u05e1\u05e8\u05d2\u05dc \u05d4\u05ea\u05e4\u05e8\u05d8\u05d9\u05dd",
"Focus to toolbar": "\u05d4\u05e2\u05d1\u05e8 \u05de\u05d9\u05e7\u05d5\u05d3 \u05dc\u05e1\u05e8\u05d2\u05dc \u05d4\u05db\u05dc\u05d9\u05dd",
"Focus to element path": "\u05e2\u05d1\u05e8 \u05de\u05d9\u05e7\u05d5\u05d3 \u05dc\u05db\u05ea\u05d5\u05d1\u05ea \u05d4\u05e4\u05e8\u05d9\u05d8",
"Focus to contextual toolbar": "\u05d4\u05e2\u05d1\u05e8 \u05de\u05d9\u05e7\u05d5\u05d3 \u05dc\u05e1\u05e8\u05d2\u05dc \u05ea\u05d5\u05db\u05df",
"Insert link (if link plugin activated)": "\u05d4\u05db\u05e0\u05e1 \u05e7\u05d9\u05e9\u05d5\u05e8 (\u05d0\u05dd \u05ea\u05d5\u05e1\u05e3 \"\u05e7\u05d9\u05e9\u05d5\u05e8\u05d9\u05dd\" \u05e4\u05e2\u05d9\u05dc)",
"Save (if save plugin activated)": "\u05e9\u05de\u05d5\u05e8 (\u05d0\u05dd \u05ea\u05d5\u05e1\u05e3 \"\u05e9\u05de\u05d9\u05e8\u05d4\" \u05e4\u05e2\u05d9\u05dc)",
"Find (if searchreplace plugin activated)": "\u05d7\u05e4\u05e9 (\u05d0\u05dd \u05ea\u05d5\u05e1\u05e3 \"\u05d7\u05e4\u05e9 \u05d5\u05d4\u05d7\u05dc\u05e3\" \u05e4\u05e2\u05d9\u05dc)",
"Plugins installed ({0}):": "\u05ea\u05d5\u05e1\u05e4\u05d9\u05dd \u05de\u05d5\u05ea\u05e7\u05e0\u05d9\u05dd ({0}):",
"Premium plugins:": "\u05ea\u05d5\u05e1\u05e4\u05d9\u05dd \u05d1\u05ea\u05e9\u05dc\u05d5\u05dd:",
"Learn more...": "\u05dc\u05de\u05d3 \u05e2\u05d5\u05d3...",
"You are using {0}": "\u05d0\u05ea\\\u05d4 \u05de\u05e9\u05ea\u05de\u05e9\\\u05ea {0}",
"Plugins": "\u05ea\u05d5\u05e1\u05e4\u05d9\u05dd",
"Handy Shortcuts": "\u05e7\u05d9\u05e6\u05d5\u05e8\u05d9\u05dd \u05e9\u05d9\u05de\u05d5\u05e9\u05d9\u05d9\u05dd",
"Horizontal line": "\u05e7\u05d5 \u05d0\u05d5\u05e4\u05e7\u05d9",
"Insert\/edit image": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05ea\u05de\u05d5\u05e0\u05d4",
"Image description": "\u05ea\u05d9\u05d0\u05d5\u05e8 \u05d4\u05ea\u05de\u05d5\u05e0\u05d4",
"Source": "\u05de\u05e7\u05d5\u05e8",
"Dimensions": "\u05de\u05d9\u05de\u05d3\u05d9\u05dd",
"Constrain proportions": "\u05d4\u05d2\u05d1\u05dc\u05ea \u05e4\u05e8\u05d5\u05e4\u05d5\u05e8\u05e6\u05d9\u05d5\u05ea",
"General": "\u05db\u05dc\u05dc\u05d9",
"Advanced": "\u05de\u05ea\u05e7\u05d3\u05dd",
"Style": "\u05e1\u05d2\u05e0\u05d5\u05df",
"Vertical space": "\u05de\u05e8\u05d5\u05d5\u05d7 \u05d0\u05e0\u05db\u05d9",
"Horizontal space": "\u05de\u05e8\u05d5\u05d5\u05d7 \u05d0\u05d5\u05e4\u05e7\u05d9",
"Border": "\u05de\u05e1\u05d2\u05e8\u05ea",
"Insert image": "\u05d4\u05db\u05e0\u05e1 \u05ea\u05de\u05d5\u05e0\u05d4",
"Image...": "\u05ea\u05de\u05d5\u05e0\u05d4...",
"Image list": "\u05e8\u05e9\u05d9\u05de\u05ea \u05ea\u05de\u05d5\u05e0\u05d5\u05ea",
"Rotate counterclockwise": "\u05e1\u05d5\u05d1\u05d1 \u05d1\u05db\u05d9\u05d5\u05d5\u05df \u05d4\u05e4\u05d5\u05da \u05dc\u05e9\u05e2\u05d5\u05df",
"Rotate clockwise": "\u05e1\u05d5\u05d1\u05d1 \u05d1\u05db\u05d9\u05d5\u05d5\u05df \u05d4\u05e9\u05e2\u05d5\u05df",
"Flip vertically": "\u05d4\u05e4\u05d5\u05da \u05d0\u05e0\u05db\u05d9\u05ea",
"Flip horizontally": "\u05d4\u05e4\u05d5\u05da \u05d0\u05d5\u05e4\u05e7\u05d9\u05ea",
"Edit image": "\u05e2\u05e8\u05d5\u05da \u05ea\u05de\u05d5\u05e0\u05d4",
"Image options": "\u05d0\u05e4\u05e9\u05e8\u05d5\u05d9\u05d5\u05ea \u05ea\u05de\u05d5\u05e0\u05d4",
"Zoom in": "\u05d4\u05d2\u05d3\u05dc \u05ea\u05e6\u05d5\u05d2\u05d4",
"Zoom out": "\u05d4\u05e7\u05d8\u05df \u05ea\u05e6\u05d5\u05d2\u05d4",
"Crop": "\u05e7\u05e6\u05e5",
"Resize": "\u05e9\u05e0\u05d4 \u05d2\u05d5\u05d3\u05dc",
"Orientation": "\u05db\u05d9\u05d5\u05d5\u05df \u05dc\u05d0\u05d5\u05e8\u05da \/ \u05dc\u05e8\u05d5\u05d7\u05d1",
"Brightness": "\u05d1\u05d4\u05d9\u05e8\u05d5\u05ea",
"Sharpen": "\u05d7\u05d3\u05d3",
"Contrast": "\u05e0\u05d9\u05d2\u05d5\u05d3\u05d9\u05d5\u05ea",
"Color levels": "\u05e8\u05de\u05d5\u05ea \u05e6\u05d1\u05e2\u05d9\u05dd",
"Gamma": "\u05d2\u05d0\u05de\u05d4",
"Invert": "\u05d4\u05d9\u05e4\u05d5\u05da \u05e6\u05d1\u05e2\u05d9\u05dd",
"Apply": "\u05d9\u05d9\u05e9\u05dd",
"Back": "\u05d7\u05d6\u05d5\u05e8",
"Insert date\/time": "\u05d4\u05db\u05e0\u05e1 \u05ea\u05d0\u05e8\u05d9\u05da\/\u05e9\u05e2\u05d4",
"Date\/time": "\u05ea\u05d0\u05e8\u05d9\u05da\/\u05e9\u05e2\u05d4",
"Insert\/Edit Link": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05e7\u05d9\u05e9\u05d5\u05e8",
"Insert\/edit link": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05e7\u05d9\u05e9\u05d5\u05e8",
"Text to display": "\u05d8\u05e7\u05e1\u05d8 \u05dc\u05d4\u05e6\u05d2\u05d4",
"Url": "\u05db\u05ea\u05d5\u05d1\u05ea \u05e7\u05d9\u05e9\u05d5\u05e8",
"Open link in...": "\u05e4\u05ea\u05d7 \u05e7\u05d9\u05e9\u05d5\u05e8 \u05d1...",
"Current window": "\u05d7\u05dc\u05d5\u05df \u05e0\u05d5\u05db\u05d7\u05d9",
"None": "\u05dc\u05dc\u05d0",
"New window": "\u05d7\u05dc\u05d5\u05df \u05d7\u05d3\u05e9",
"Remove link": "\u05de\u05d7\u05e7 \u05e7\u05d9\u05e9\u05d5\u05e8",
"Anchors": "\u05e2\u05d5\u05d2\u05e0\u05d9\u05dd",
"Link...": "\u05e7\u05d9\u05e9\u05d5\u05e8...",
"Paste or type a link": "\u05d4\u05d3\u05d1\u05e7 \u05d0\u05d5 \u05d4\u05e7\u05dc\u05d3 \u05e7\u05d9\u05e9\u05d5\u05e8",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u05e0\u05e8\u05d0\u05d4 \u05e9\u05d4\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d4\u05db\u05e0\u05e1\u05ea \u05d4\u05d9\u05d0 \u05db\u05ea\u05d5\u05d1\u05ea \u05d0\u05d9\u05de\u05d9\u05d9\u05dc. \u05d4\u05d0\u05dd \u05d1\u05e8\u05e6\u05d5\u05e0\u05da \u05dc\u05d4\u05d5\u05e1\u05d9\u05e3 \u05d0\u05ea \u05d4\u05e7\u05d9\u05d3\u05d5\u05de\u05ea :mailto?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u05e0\u05e8\u05d0\u05d4 \u05e9\u05d4\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d4\u05db\u05e0\u05e1\u05ea \u05d4\u05d9\u05d0 \u05e7\u05d9\u05e9\u05d5\u05e8 \u05d7\u05d9\u05e6\u05d5\u05e0\u05d9 \u05d4\u05d0\u05dd \u05d1\u05e8\u05e6\u05d5\u05e0\u05da \u05dc\u05d4\u05d5\u05e1\u05d9\u05e3 \u05e7\u05d9\u05d3\u05d5\u05de\u05ea http:\/\/?",
"Link list": "\u05e8\u05e9\u05d9\u05de\u05ea \u05e7\u05d9\u05e9\u05d5\u05e8\u05d9\u05dd",
"Insert video": "\u05d4\u05db\u05e0\u05e1 \u05e1\u05e8\u05d8\u05d5\u05df",
"Insert\/edit video": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05e1\u05e8\u05d8\u05d5\u05df",
"Insert\/edit media": "\u05d4\u05db\u05e0\u05e1\/\u05e2\u05e8\u05d5\u05da \u05de\u05d3\u05d9\u05d4",
"Alternative source": "\u05de\u05e7\u05d5\u05e8 \u05de\u05e9\u05e0\u05d9",
"Alternative source URL": "\u05db\u05ea\u05d5\u05d1\u05ea URL \u05dc\u05de\u05e7\u05d5\u05e8 \u05d7\u05dc\u05d5\u05e4\u05d9",
"Media poster (Image URL)": "\u05e4\u05d5\u05e1\u05d8\u05e8 \u05de\u05d3\u05d9\u05d4 (\u05db\u05ea\u05d5\u05d1\u05ea URL \u05dc\u05ea\u05de\u05d5\u05e0\u05d4)",
"Paste your embed code below:": "\u05d4\u05d3\u05d1\u05e7 \u05e7\u05d5\u05d3 \u05d4\u05d8\u05de\u05e2\u05d4 \u05de\u05ea\u05d7\u05ea:",
"Embed": "\u05d4\u05d8\u05de\u05e2",
"Media...": "\u05de\u05d3\u05d9\u05d4...",
"Nonbreaking space": "\u05e8\u05d5\u05d5\u05d7 (\u05dc\u05dc\u05d0 \u05e9\u05d1\u05d9\u05e8\u05ea \u05e9\u05d5\u05e8\u05d4)",
"Page break": "\u05d3\u05e3 \u05d7\u05d3\u05e9",
"Paste as text": "\u05d4\u05d3\u05d1\u05e7 \u05db\u05d8\u05e7\u05e1\u05d8",
"Preview": "\u05ea\u05e6\u05d5\u05d2\u05d4 \u05de\u05e7\u05d3\u05d9\u05de\u05d4",
"Print...": "\u05d4\u05d3\u05e4\u05e1...",
"Save": "\u05e9\u05de\u05d9\u05e8\u05d4",
"Find": "\u05d7\u05e4\u05e9",
"Replace with": "\u05d4\u05d7\u05dc\u05e3 \u05d1",
"Replace": "\u05d4\u05d7\u05dc\u05e3",
"Replace all": "\u05d4\u05d7\u05dc\u05e3 \u05d4\u05db\u05dc",
"Previous": "\u05d4\u05e7\u05d5\u05d3\u05dd",
"Next": "\u05d4\u05d1\u05d0",
"Find and replace...": "\u05d7\u05d9\u05e4\u05d5\u05e9 \u05d5\u05d4\u05d7\u05dc\u05e4\u05d4...",
"Could not find the specified string.": "\u05de\u05d7\u05e8\u05d5\u05d6\u05ea \u05dc\u05d0 \u05e0\u05de\u05e6\u05d0\u05d4",
"Match case": "\u05d4\u05d1\u05d7\u05df \u05d1\u05d9\u05df \u05d0\u05d5\u05ea\u05d9\u05d5\u05ea \u05e7\u05d8\u05e0\u05d5\u05ea \u05dc\u05d2\u05d3\u05d5\u05dc\u05d5\u05ea",
"Find whole words only": "\u05d7\u05e4\u05e9 \u05de\u05d9\u05dc\u05d9\u05dd \u05e9\u05dc\u05de\u05d5\u05ea \u05d1\u05dc\u05d1\u05d3",
"Spell check": "\u05d1\u05d3\u05d9\u05e7\u05ea \u05d0\u05d9\u05d5\u05ea",
"Ignore": "\u05d4\u05ea\u05e2\u05dc\u05dd",
"Ignore all": "\u05d4\u05ea\u05e2\u05dc\u05dd \u05de\u05d4\u05db\u05dc",
"Finish": "\u05e1\u05d9\u05d9\u05dd",
"Add to Dictionary": "\u05d4\u05d5\u05e1\u05e3 \u05dc\u05de\u05d9\u05dc\u05d5\u05df",
"Insert table": "\u05d4\u05db\u05e0\u05e1 \u05d8\u05d1\u05dc\u05d4",
"Table properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05d8\u05d1\u05dc\u05d4",
"Delete table": "\u05de\u05d7\u05e7 \u05d8\u05d1\u05dc\u05d4",
"Cell": "\u05ea\u05d0",
"Row": "\u05e9\u05d5\u05e8\u05d4",
"Column": "\u05e2\u05de\u05d5\u05d3\u05d4",
"Cell properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05ea\u05d0",
"Merge cells": "\u05de\u05d6\u05d2 \u05ea\u05d0\u05d9\u05dd",
"Split cell": "\u05e4\u05e6\u05dc \u05ea\u05d0",
"Insert row before": "\u05d4\u05d5\u05e1\u05e3 \u05e9\u05d5\u05e8\u05d4 \u05dc\u05e4\u05e0\u05d9",
"Insert row after": "\u05d4\u05d5\u05e1\u05e3 \u05e9\u05d5\u05e8\u05d4 \u05d0\u05d7\u05e8\u05d9",
"Delete row": "\u05de\u05d7\u05e7 \u05e9\u05d5\u05e8\u05d4",
"Row properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05e9\u05d5\u05e8\u05d4",
"Cut row": "\u05d2\u05d6\u05d5\u05e8 \u05e9\u05d5\u05e8\u05d4",
"Copy row": "\u05d4\u05e2\u05ea\u05e7 \u05e9\u05d5\u05e8\u05d4",
"Paste row before": "\u05d4\u05d3\u05d1\u05e7 \u05e9\u05d5\u05e8\u05d4 \u05dc\u05e4\u05e0\u05d9",
"Paste row after": "\u05d4\u05e2\u05ea\u05e7 \u05e9\u05d5\u05e8\u05d4 \u05d0\u05d7\u05e8\u05d9",
"Insert column before": "\u05d4\u05e2\u05ea\u05e7 \u05e2\u05de\u05d5\u05d3\u05d4 \u05dc\u05e4\u05e0\u05d9",
"Insert column after": "\u05d4\u05e2\u05ea\u05e7 \u05e2\u05de\u05d5\u05d3\u05d4 \u05d0\u05d7\u05e8\u05d9",
"Delete column": "\u05de\u05d7\u05e7 \u05e2\u05de\u05d5\u05d3\u05d4",
"Cols": "\u05e2\u05de\u05d5\u05d3\u05d5\u05ea",
"Rows": "\u05e9\u05d5\u05e8\u05d5\u05ea",
"Width": "\u05e8\u05d5\u05d7\u05d1",
"Height": "\u05d2\u05d5\u05d1\u05d4",
"Cell spacing": "\u05e9\u05d5\u05dc\u05d9\u05d9\u05dd \u05d7\u05d9\u05e6\u05d5\u05e0\u05d9\u05dd \u05dc\u05ea\u05d0",
"Cell padding": "\u05e9\u05d5\u05dc\u05d9\u05d9\u05dd \u05e4\u05e0\u05d9\u05de\u05d9\u05d9\u05dd \u05dc\u05ea\u05d0",
"Show caption": "\u05d4\u05e6\u05d2 \u05db\u05ea\u05d5\u05d1\u05d9\u05ea",
"Left": "\u05e9\u05de\u05d0\u05dc",
"Center": "\u05de\u05e8\u05db\u05d6",
"Right": "\u05d9\u05de\u05d9\u05df",
"Cell type": "\u05e1\u05d5\u05d2 \u05ea\u05d0",
"Scope": "\u05d4\u05d9\u05e7\u05e3",
"Alignment": "\u05d9\u05d9\u05e9\u05d5\u05e8",
"H Align": "\u05d9\u05d9\u05e9\u05d5\u05e8 \u05d0\u05d5\u05e4\u05e7\u05d9",
"V Align": "\u05d9\u05d9\u05e9\u05d5\u05e8 \u05d0\u05e0\u05db\u05d9",
"Top": "\u05e2\u05dc\u05d9\u05d5\u05df",
"Middle": "\u05d0\u05de\u05e6\u05e2",
"Bottom": "\u05ea\u05d7\u05ea\u05d9\u05ea",
"Header cell": "\u05db\u05d5\u05ea\u05e8\u05ea \u05dc\u05ea\u05d0",
"Row group": "\u05e7\u05d9\u05d1\u05d5\u05e5 \u05e9\u05d5\u05e8\u05d5\u05ea",
"Column group": "\u05e7\u05d9\u05d1\u05d5\u05e5 \u05e2\u05de\u05d5\u05d3\u05d5\u05ea",
"Row type": "\u05e1\u05d5\u05d2 \u05e9\u05d5\u05e8\u05d4",
"Header": "\u05db\u05d5\u05ea\u05e8\u05ea",
"Body": "\u05d2\u05d5\u05e3 \u05d4\u05d8\u05d1\u05dc\u05d0",
"Footer": "\u05db\u05d5\u05ea\u05e8\u05ea \u05ea\u05d7\u05ea\u05d5\u05e0\u05d4",
"Border color": "\u05e6\u05d1\u05e2 \u05d2\u05d1\u05d5\u05dc",
"Insert template...": "\u05d4\u05db\u05e0\u05e1 \u05ea\u05d1\u05e0\u05d9\u05ea...",
"Templates": "\u05ea\u05d1\u05e0\u05d9\u05d5\u05ea",
"Template": "\u05ea\u05d1\u05e0\u05d9\u05ea",
"Text color": "\u05e6\u05d1\u05e2 \u05d4\u05db\u05ea\u05d1",
"Background color": "\u05e6\u05d1\u05e2 \u05e8\u05e7\u05e2",
"Custom...": "\u05de\u05d5\u05ea\u05d0\u05dd \u05d0\u05d9\u05e9\u05d9\u05ea...",
"Custom color": "\u05e6\u05d1\u05e2 \u05de\u05d5\u05ea\u05d0\u05dd \u05d0\u05d9\u05e9\u05d9\u05ea",
"No color": "\u05dc\u05dc\u05d0 \u05e6\u05d1\u05e2",
"Remove color": "\u05d4\u05e1\u05e8 \u05e6\u05d1\u05e2",
"Table of Contents": "\u05ea\u05d5\u05db\u05df \u05e2\u05e0\u05d9\u05d9\u05e0\u05d9\u05dd",
"Show blocks": "\u05d4\u05e6\u05d2 \u05ea\u05d9\u05d1\u05d5\u05ea",
"Show invisible characters": "\u05d4\u05e6\u05d2 \u05ea\u05d5\u05d5\u05d9\u05dd \u05dc\u05d0 \u05e0\u05e8\u05d0\u05d9\u05dd",
"Word count": "\u05e1\u05e4\u05d9\u05e8\u05ea \u05de\u05d9\u05dc\u05d9\u05dd",
"Words: {0}": "\u05de\u05d9\u05dc\u05d9\u05dd: {0}",
"{0} words": "{0} \u05de\u05d9\u05dc\u05d9\u05dd",
"File": "\u05e7\u05d5\u05d1\u05e5",
"Edit": "\u05e2\u05e8\u05d9\u05db\u05d4",
"Insert": "\u05d4\u05d5\u05e1\u05e4\u05d4",
"View": "\u05ea\u05e6\u05d5\u05d2\u05d4",
"Format": "\u05e4\u05d5\u05e8\u05de\u05d8",
"Table": "\u05d8\u05d1\u05dc\u05d4",
"Tools": "\u05db\u05dc\u05d9\u05dd",
"Powered by {0}": "\u05de\u05d5\u05e4\u05e2\u05dc \u05e2\"\u05d9 {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\u05ea\u05d9\u05d1\u05ea \u05e2\u05e8\u05d9\u05db\u05d4 \u05d7\u05db\u05de\u05d4. \u05dc\u05d7\u05e5 Alt-F9 \u05dc\u05ea\u05e4\u05e8\u05d9\u05d8. Alt-F10 \u05dc\u05ea\u05e6\u05d5\u05d2\u05ea \u05db\u05e4\u05ea\u05d5\u05e8\u05d9\u05dd, Alt-0 \u05dc\u05e2\u05d6\u05e8\u05d4",
"Image title": "\u05db\u05d5\u05ea\u05e8\u05ea \u05ea\u05de\u05d5\u05e0\u05d4",
"Border width": "\u05e8\u05d5\u05d7\u05d1 \u05d2\u05d1\u05d5\u05dc",
"Border style": "\u05e1\u05d2\u05e0\u05d5\u05df \u05d2\u05d1\u05d5\u05dc",
"Error": "\u05e9\u05d2\u05d9\u05d0\u05d4",
"Warn": "\u05d0\u05d6\u05d4\u05e8\u05d4",
"Valid": "\u05d7\u05d5\u05e7\u05d9",
"To open the popup, press Shift+Enter": "\u05db\u05d3\u05d9 \u05dc\u05e4\u05ea\u05d5\u05d7 \u05d0\u05ea \u05d4\u05e4\u05e8\u05d9\u05d8 \u05d4\u05de\u05d5\u05e7\u05e4\u05e5, \u05d4\u05e7\u05e9 \u05e2\u05dc Shift+Enter",
"Rich Text Area. Press ALT-0 for help.": "\u05d0\u05d6\u05d5\u05e8 \u05d8\u05e7\u05e1\u05d8 \u05e2\u05e9\u05d9\u05e8. \u05d4\u05e7\u05e9 \u05e2\u05dc ALT-0 \u05dc\u05e2\u05d6\u05e8\u05d4.",
"System Font": "\u05d2\u05d5\u05e4\u05df \u05de\u05e2\u05e8\u05db\u05ea",
"Failed to upload image: {0}": "\u05db\u05e9\u05dc \u05d1\u05d4\u05e2\u05dc\u05d0\u05ea \u05ea\u05de\u05d5\u05e0\u05d4: {0}",
"Failed to load plugin: {0} from url {1}": "\u05db\u05e9\u05dc \u05d1\u05d8\u05e2\u05d9\u05e0\u05ea \u05d9\u05d9\u05e9\u05d5\u05dd Plugin: {0} \u05de\u05db\u05ea\u05d5\u05d1\u05ea URL\u200f {1}",
"Failed to load plugin url: {0}": "\u05db\u05e9\u05dc \u05d1\u05d8\u05e2\u05d9\u05e0\u05ea \u05db\u05ea\u05d5\u05d1\u05ea URL \u05e9\u05dc \u05d9\u05d9\u05e9\u05d5\u05dd Plugin\u200f: {0}",
"Failed to initialize plugin: {0}": "\u05db\u05e9\u05dc \u05d1\u05d0\u05ea\u05d7\u05d5\u05dc \u05d9\u05d9\u05e9\u05d5\u05dd Plugin\u200f: {0}",
"example": "\u05d3\u05d5\u05d2\u05de\u05d4",
"Search": "\u05d7\u05e4\u05e9",
"All": "\u05d4\u05db\u05dc",
"Currency": "\u05de\u05d8\u05d1\u05e2",
"Text": "\u05d8\u05e7\u05e1\u05d8",
"Quotations": "\u05e9\u05d0\u05dc\u05d5\u05ea",
"Mathematical": "\u05de\u05ea\u05de\u05d8\u05d9",
"Extended Latin": "\u05dc\u05d8\u05d9\u05e0\u05d9\u05ea \u05de\u05d5\u05e8\u05d7\u05d1\u05ea",
"Symbols": "\u05e1\u05de\u05dc\u05d9\u05dd",
"Arrows": "\u05d7\u05d9\u05e6\u05d9\u05dd",
"User Defined": "\u05de\u05d5\u05d2\u05d3\u05e8 \u05e2\u05dc-\u05d9\u05d3\u05d9 \u05d4\u05de\u05e9\u05ea\u05de\u05e9",
"dollar sign": "\u05e1\u05d9\u05de\u05df \u05d3\u05d5\u05dc\u05e8",
"currency sign": "\u05e1\u05d9\u05de\u05df \u05de\u05d8\u05d1\u05e2",
"euro-currency sign": "\u05e1\u05d9\u05de\u05df \u05de\u05d8\u05d1\u05e2 \u05d0\u05d9\u05e8\u05d5",
"colon sign": "\u05e1\u05d9\u05de\u05df \u05e7\u05d5\u05dc\u05d5\u05df",
"cruzeiro sign": "\u05e1\u05d9\u05de\u05df \u05e7\u05e8\u05d5\u05d6\u05e8\u05d5",
"french franc sign": "\u05e1\u05d9\u05de\u05df \u05e4\u05e8\u05e0\u05e7 \u05e6\u05e8\u05e4\u05ea\u05d9",
"lira sign": "\u05e1\u05d9\u05de\u05df \u05dc\u05d9\u05e8\u05d4",
"mill sign": "\u05e1\u05d9\u05de\u05df \u05de\u05d9\u05dc",
"naira sign": "\u05e1\u05d9\u05de\u05df \u05e0\u05d0\u05d9\u05e8\u05d4",
"peseta sign": "\u05e1\u05d9\u05de\u05df \u05e4\u05d6\u05d8\u05d4",
"rupee sign": "\u05e1\u05d9\u05de\u05df \u05e8\u05d5\u05e4\u05d9",
"won sign": "\u05e1\u05d9\u05de\u05df \u05d5\u05d5\u05df",
"new sheqel sign": "\u05e1\u05d9\u05de\u05df \u05e9\u05e7\u05dc \u05d7\u05d3\u05e9",
"dong sign": "\u05e1\u05d9\u05de\u05df \u05d3\u05d5\u05e0\u05d2",
"kip sign": "\u05e1\u05d9\u05de\u05df \u05e7\u05d9\u05e4",
"tugrik sign": "\u05e1\u05d9\u05de\u05df \u05d8\u05d5\u05d2\u05e8\u05d9\u05e7",
"drachma sign": "\u05e1\u05d9\u05de\u05df \u05d3\u05e8\u05db\u05de\u05d4",
"german penny symbol": "\u05e1\u05de\u05dc \u05e4\u05e0\u05d9 \u05d2\u05e8\u05de\u05e0\u05d9",
"peso sign": "\u05e1\u05d9\u05de\u05df \u05e4\u05d6\u05d5",
"guarani sign": "\u05e1\u05d9\u05de\u05df \u05d2\u05d5\u05d0\u05e8\u05e0\u05d9\u05ea",
"austral sign": "\u05e1\u05d9\u05de\u05df \u05d0\u05d5\u05e1\u05d8\u05e8\u05dc",
"hryvnia sign": "\u05e1\u05d9\u05de\u05df \u05e8\u05d9\u05d1\u05e0\u05d9\u05d4",
"cedi sign": "\u05e1\u05d9\u05de\u05df \u05e1\u05d3\u05d9",
"livre tournois sign": "\u05e1\u05d9\u05de\u05df \u05dc\u05d1\u05e8\u05d4 \u05d8\u05d5\u05e8\u05e0\u05d5",
"spesmilo sign": "\u05e1\u05d9\u05de\u05df \u05e1\u05e4\u05e1\u05de\u05d9\u05dc\u05d5",
"tenge sign": "\u05e1\u05d9\u05de\u05df \u05d8\u05e0\u05d2\u05d4",
"indian rupee sign": "\u05e1\u05d9\u05de\u05df \u05e8\u05d5\u05e4\u05d9 \u05d4\u05d5\u05d3\u05d9",
"turkish lira sign": "\u05e1\u05d9\u05de\u05df \u05dc\u05d9\u05e8\u05d4 \u05d8\u05d5\u05e8\u05e7\u05d9\u05ea",
"nordic mark sign": "\u05e1\u05d9\u05de\u05df \u05de\u05d0\u05e8\u05e7 \u05e1\u05e7\u05e0\u05d3\u05d9\u05e0\u05d1\u05d9",
"manat sign": "\u05e1\u05d9\u05de\u05df \u05de\u05d0\u05e0\u05d0\u05d8",
"ruble sign": "\u05e1\u05d9\u05de\u05df \u05e8\u05d5\u05d1\u05dc",
"yen character": "\u05ea\u05d5 \u05d9\u05df",
"yuan character": "\u05ea\u05d5 \u05d9\u05d5\u05d0\u05df",
"yuan character, in hong kong and taiwan": "\u05ea\u05d5 \u05d9\u05d5\u05d0\u05df, \u05d1\u05d4\u05d5\u05e0\u05d2 \u05e7\u05d5\u05e0\u05d2 \u05d5\u05d1\u05d8\u05d9\u05d9\u05d5\u05d5\u05d0\u05df",
"yen\/yuan character variant one": "\u05de\u05e9\u05ea\u05e0\u05d4 \u05d0\u05d7\u05d3 \u05e9\u05dc \u05ea\u05d5 \u05d9\u05d5\u05d0\u05df\/\u05d9\u05df",
"Loading emoticons...": "\u05d8\u05d5\u05e2\u05df \u05e1\u05de\u05dc\u05d9 \u05d4\u05d1\u05e2\u05d4...",
"Could not load emoticons": "\u05dc\u05d0 \u05e0\u05d9\u05ea\u05df \u05d4\u05d9\u05d4 \u05dc\u05d8\u05e2\u05d5\u05df \u05e1\u05de\u05dc\u05d9 \u05d4\u05d1\u05e2\u05d4",
"People": "\u05d0\u05e0\u05e9\u05d9\u05dd",
"Animals and Nature": "\u05d1\u05e2\u05dc\u05d9-\u05d7\u05d9\u05d9\u05dd \u05d5\u05d8\u05d1\u05e2",
"Food and Drink": "\u05d0\u05d5\u05db\u05dc \u05d5\u05e9\u05ea\u05d9\u05d9\u05d4",
"Activity": "\u05e4\u05e2\u05d9\u05dc\u05d5\u05ea",
"Travel and Places": "\u05e0\u05e1\u05d9\u05e2\u05d4 \u05d5\u05de\u05e7\u05d5\u05de\u05d5\u05ea",
"Objects": "\u05d0\u05d5\u05d1\u05d9\u05d9\u05e7\u05d8\u05d9\u05dd",
"Flags": "\u05d3\u05d2\u05dc\u05d9\u05dd",
"Characters": "\u05ea\u05d5\u05d5\u05d9\u05dd",
"Characters (no spaces)": "\u05ea\u05d5\u05d5\u05d9\u05dd (\u05dc\u05dc\u05d0 \u05e8\u05d5\u05d5\u05d7\u05d9\u05dd)",
"Error: Form submit field collision.": "\u05e9\u05d2\u05d9\u05d0\u05d4: \u05d4\u05ea\u05e0\u05d2\u05e9\u05d5\u05ea \u05d1\u05e9\u05d3\u05d4 \u05e9\u05dc\u05d9\u05d7\u05ea \u05d8\u05d5\u05e4\u05e1.",
"Error: No form element found.": "\u05e9\u05d2\u05d9\u05d0\u05d4: \u05dc\u05d0 \u05e0\u05de\u05e6\u05d0 \u05e8\u05db\u05d9\u05d1 \u05d8\u05d5\u05e4\u05e1.",
"Update": "\u05e2\u05d3\u05db\u05df",
"Color swatch": "\u05d3\u05d5\u05d2\u05de\u05d0\u05d5\u05ea \u05e6\u05d1\u05e2",
"Turquoise": "\u05d8\u05d5\u05e8\u05e7\u05d9\u05d6",
"Green": "\u05d9\u05e8\u05d5\u05e7",
"Blue": "\u05db\u05d7\u05d5\u05dc",
"Purple": "\u05e1\u05d2\u05d5\u05dc",
"Navy Blue": "\u05db\u05d7\u05d5\u05dc \u05e6\u05d9",
"Dark Turquoise": "\u05d8\u05d5\u05e8\u05e7\u05d9\u05d6 \u05db\u05d4\u05d4",
"Dark Green": "\u05d9\u05e8\u05d5\u05e7 \u05db\u05d4\u05d4",
"Medium Blue": "\u05db\u05d7\u05d5\u05dc \u05d1\u05d9\u05e0\u05d5\u05e0\u05d9",
"Medium Purple": "\u05e1\u05d2\u05d5\u05dc \u05d1\u05d9\u05e0\u05d5\u05e0\u05d9",
"Midnight Blue": "\u05db\u05d7\u05d5\u05dc \u05d7\u05e6\u05d5\u05ea",
"Yellow": "\u05e6\u05d4\u05d5\u05d1",
"Orange": "\u05db\u05ea\u05d5\u05dd",
"Red": "\u05d0\u05d3\u05d5\u05dd",
"Light Gray": "\u05d0\u05e4\u05d5\u05e8 \u05d1\u05d4\u05d9\u05e8",
"Gray": "\u05d0\u05e4\u05d5\u05e8",
"Dark Yellow": "\u05e6\u05d4\u05d5\u05d1 \u05db\u05d4\u05d4",
"Dark Orange": "\u05db\u05ea\u05d5\u05dd \u05db\u05d4\u05d4",
"Dark Red": "\u05d0\u05d3\u05d5\u05dd \u05db\u05d4\u05d4",
"Medium Gray": "\u05d0\u05e4\u05d5\u05e8 \u05d1\u05d9\u05e0\u05d5\u05e0\u05d9",
"Dark Gray": "\u05d0\u05e4\u05d5\u05e8 \u05db\u05d4\u05d4",
"Black": "\u05e9\u05d7\u05d5\u05e8",
"White": "\u05dc\u05d1\u05df",
"Switch to or from fullscreen mode": "\u05d4\u05d7\u05dc\u05e3 \u05dc\u05de\u05e6\u05d1 \u05de\u05e1\u05da \u05de\u05dc\u05d0 \u05d0\u05d5 \u05e6\u05d0 \u05de\u05de\u05e0\u05d5",
"Open help dialog": "\u05e4\u05ea\u05d7 \u05ea\u05d9\u05d1\u05ea \u05d3\u05d5-\u05e9\u05d9\u05d7 \u05e9\u05dc \u05e2\u05d6\u05e8\u05d4",
"history": "\u05d4\u05d9\u05e1\u05d8\u05d5\u05e8\u05d9\u05d4",
"styles": "\u05e1\u05d2\u05e0\u05d5\u05e0\u05d5\u05ea",
"formatting": "\u05e2\u05d9\u05e6\u05d5\u05d1",
"alignment": "\u05d9\u05d9\u05e9\u05d5\u05e8",
"indentation": "\u05d4\u05d6\u05d7\u05d4",
"permanent pen": "\u05e2\u05d8 \u05e7\u05d1\u05d5\u05e2",
"comments": "\u05d4\u05e2\u05e8\u05d5\u05ea",
"Anchor": "\u05de\u05e7\u05d5\u05dd \u05e2\u05d9\u05d2\u05d5\u05df",
"Special character": "\u05ea\u05d5\u05d5\u05d9\u05dd \u05de\u05d9\u05d5\u05d7\u05d3\u05d9\u05dd",
"Code sample": "\u05d3\u05d5\u05d2\u05de\u05ea \u05e7\u05d5\u05d3",
"Color": "\u05e6\u05d1\u05e2",
"Emoticons": "\u05de\u05d7\u05d5\u05d5\u05ea",
"Document properties": "\u05de\u05d0\u05e4\u05d9\u05d9\u05e0\u05d9 \u05de\u05e1\u05de\u05da",
"Image": "\u05ea\u05de\u05d5\u05e0\u05d4",
"Insert link": "\u05d4\u05db\u05e0\u05e1 \u05e7\u05d9\u05e9\u05d5\u05e8",
"Target": "\u05de\u05d8\u05e8\u05d4",
"Link": "\u05e7\u05d9\u05e9\u05d5\u05e8",
"Poster": "\u05e4\u05d5\u05e1\u05d8\u05e8",
"Media": "\u05de\u05d3\u05d9\u05d4",
"Print": "\u05d4\u05d3\u05e4\u05e1",
"Prev": "\u05e7\u05d5\u05d3\u05dd",
"Find and replace": "\u05d7\u05e4\u05e9 \u05d5\u05d4\u05d7\u05dc\u05e3",
"Whole words": "\u05de\u05d9\u05dc\u05d4 \u05e9\u05dc\u05de\u05d4",
"Spellcheck": "\u05d1\u05d5\u05d3\u05e7 \u05d0\u05d9\u05d5\u05ea",
"Caption": "\u05db\u05d9\u05ea\u05d5\u05d1",
"Insert template": "\u05d4\u05db\u05e0\u05e1 \u05ea\u05d1\u05e0\u05d9\u05ea",
"_dir": "rtl"
});