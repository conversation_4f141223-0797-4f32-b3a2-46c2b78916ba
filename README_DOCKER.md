# Docker Development Setup

## Overview

This Docker setup provides a simple, clean local development environment that exactly mirrors your Elastic Beanstalk AL2023 Python 3.12 production environment.

## Quick Start

```bash
# Start the development environment
docker-compose up

# Your application will be available at:
# http://localhost:8002
```

## What's Included

- **Web Application**: Python 3.12 (matches EB AL2023 v4.6.0)
- **Database**: PostgreSQL 15
- **Cache**: Redis
- **Static Files**: Automatically collected and served

## Environment Details

### Production Parity
- **Python Version**: 3.12 (same as EB AL2023 v4.6.0)
- **Package Versions**: Identical to production via requirements.txt
- **Environment Variables**: Same as production
- **Database Setup**: Matches production schema

### Services
- **web**: Main Django application on port 8002
- **db**: PostgreSQL database on port 5432
- **redis**: Redis cache on port 6379

## Commands

### Development
```bash
# Start all services
docker-compose up

# Start in background
docker-compose up -d

# View logs
docker-compose logs web

# Rebuild after code changes
docker-compose build web

# Stop services
docker-compose down
```

### Database
```bash
# Reset database (removes all data)
docker-compose down -v
docker-compose up

# Access database directly
docker-compose exec db psql -U postgres -d expo
```

### Cleanup
```bash
# Stop and remove everything
docker-compose down -v --rmi all

# Clean Docker cache
docker system prune -f
```

## File Structure

```
.
├── docker-compose.yml          # Docker services configuration
├── Dockerfile                  # Python 3.12 application container
├── requirements.txt           # Python dependencies
├── .ebextensions/            # EB deployment configurations
│   ├── 01_python.config      # Python/Django settings
│   └── 02_db-migrate.config  # Database migrations
└── README_DOCKER.md          # This file
```

## Deployment

This setup ensures your local environment exactly matches production:

1. **Same Python version** (3.12)
2. **Same packages** (requirements.txt)
3. **Same configurations** (.ebextensions)
4. **Same environment variables**

Deploy with confidence knowing your code works identically in both environments!

## Troubleshooting

### Build Issues
```bash
# Clean rebuild
docker-compose build --no-cache web
```

### Database Connection Issues
```bash
# Check database is running
docker-compose ps
# Reset database
docker-compose down -v && docker-compose up
```

### Port Conflicts
If port 8002 is in use, change it in docker-compose.yml:
```yaml
ports:
  - "8003:8002"  # Use port 8003 instead
```
