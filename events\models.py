from django_tenants.models import TenantMixin, DomainMixin
from tinymce import models as tinymce_models

from django.db import models
from django.utils import timezone

from users.models import CustomUser as User
from accounts.models import Account, Access
from jwt_shared_secret.models import JWTSecret


class Event(TenantMixin):
    account = models.ForeignKey(
        Account, on_delete=models.SET_NULL, blank=True, null=True, related_name="events")
    access = models.ForeignKey(
        Access, on_delete=models.SET_NULL, blank=True, null=True, related_name="event_access"
    )
    name = models.CharField(max_length=100)
    domain_url = models.Char<PERSON>ield(max_length=128, unique=True, blank=True, null=True)
    frontend_sub_domain = models.Char<PERSON>ield(max_length=100, null=True, unique=True)
    login_title = models.Char<PERSON>ield(max_length=200, blank=True, null=True)
    browser_title = models.CharField(max_length=100, blank=True, null=True)
    login_logo = models.ImageField(upload_to='login_logos', blank=True, null=True)
    image_alt_text = models.CharField(max_length=300, blank=True, null=True, help_text="Optionally add descriptive alternative text to the logo. If blank event title will be used.")
    login_description = models.TextField(blank=True, null=True, help_text="This feature will be deprecated on July 1 2021. Please use the widget below for new events and only use this one to edit.")
    html_blurb = tinymce_models.HTMLField(blank=True, null=True, verbose_name="Event Side Panel Description", help_text="Please use this editor to change text in the event home page. If you use this editor the old description will be repalced ")
    event_password = models.CharField(max_length=100, blank=True, null=True)
    background_color = models.CharField(max_length=9, blank=True, null=True)
    pre_registration_on = models.BooleanField(default=False, help_text="This sends users to a confirmation page and send an email. Also does not allow attendees to login.")
    no_registration = models.BooleanField(default=False, help_text='This restricts users from being able to register on the frontend.')
    
    start_datetime = models.DateTimeField(blank=True, null=True)
    registration_confirmation = models.BooleanField(default=False, help_text='This enables autoresponse registration confirmation emails to attendees upon registration.')
    reg_confirm_email_html = tinymce_models.HTMLField(blank=True, null=True, verbose_name="Registration Confirmation Email HTML", help_text="Use this editor to insert a custom registration confirmation email")
    
    change_password = models.BooleanField(default=True, help_text='This allows users to be able to change their password on the frontend.')
    default_space_id = models.CharField(max_length=100, blank=True, null=True)
    team_name = models.CharField(max_length=100, null=True, blank=True)
    dark_mode = models.BooleanField(default=True)
    participants_tab = models.BooleanField(default=True)
    private_messaging = models.BooleanField(default=True)
    
    EXPOHALL = 'EXPO'
    AUDITORIUM = 'AUD'
    CUSTOM = 'CUSTOM'
    LOBBY = 'LOBBY'
    SCHEDULE = 'SCHEDULE'
    SPACE_CHOICES = (
        (EXPOHALL, 'EXPO'),
        (AUDITORIUM, 'AUD'),
        (CUSTOM, 'CUSTOM'),
        (LOBBY, 'LOBBY'),
        (SCHEDULE, 'SCHEDULE'),
    )
    
    default_space_type = models.CharField(max_length=8, choices=SPACE_CHOICES, default=EXPOHALL)

    room_id = models.IntegerField(default=20)
    num_logins = models.IntegerField(default=3, help_text="The number of concurrent logins allowed by the same user.")
    auto_create_schema = True
    time_created = models.DateTimeField(default=timezone.now)
    jwt_shared_secret = models.ForeignKey(
        JWTSecret, on_delete=models.CASCADE, related_name='events', null=True, blank=True)

    accent_bright = models.CharField(max_length=50,default='rgb(255, 0, 0)', help_text="Change accent colour.")
    accent_light = models.CharField(max_length=50,default='rgb(204, 0, 0)', help_text="Change accent colour.")
    accent_medium = models.CharField(max_length=50,default='rgb(153, 0, 0)', help_text="Change accent colour.")
    accent_dark = models.CharField(max_length=50,default='rgb(110, 0, 0)', help_text="Change accent colour.")

    time_updated = models.DateTimeField(default=timezone.now)
    edition_mode = models.BooleanField(default=False)
    edition_mode_by = models.ForeignKey(
        User, on_delete=models.DO_NOTHING, related_name='events', null=True, blank=True)
    editing_by_username = models.CharField(max_length=250, blank=True, null=True)

    def __str__(self):
        return self.name

    @classmethod
    def from_db(cls, db, field_names, values):
        instance = super().from_db(db, field_names, values)
        # save original values, when model is loaded from database,
        # in a separate attribute on the model
        instance._loaded_values = dict(zip(field_names, values))
        return instance

    def save(self, *args, **kwargs):
        # check if a new db row is being added
        # When this happens the `_loaded_values` attribute will not be available
        if not self._state.adding:
            # check if field_1 is being updated
            if hasattr(self, '_loaded_values') and self._loaded_values['edition_mode'] == True and hasattr(self, 'force_update') and self.force_update != True:
                if hasattr(self, 'edition_mode_by_id') and self._loaded_values['edition_mode_by_id'] != self.edition_mode_by_id:
                    message = " ".join(['Event is in edition mode by', str(self._loaded_values['edition_mode_by_id'])])
                    raise Exception(message, self._loaded_values)

        if not self.frontend_sub_domain and self.name != 'public':
            domain_url = self.domain_url
            frontend_sub_domain = domain_url.split('.')[0]
            self.frontend_sub_domain = frontend_sub_domain
        if not self.image_alt_text and self.login_logo:
            self.image_alt_text = self.name
        if not self.team_name:
            self.team_name = self.frontend_sub_domain
        
        self.time_updated = timezone.now()
        super().save(*args, **kwargs)
        return self

    # zoom enabled, chat enabled, config file, business cards enabled, form button enabled
    # participants

class Domain(DomainMixin):
    tenant = models.ForeignKey(Event, on_delete=models.CASCADE, related_name='domains')
    pass
