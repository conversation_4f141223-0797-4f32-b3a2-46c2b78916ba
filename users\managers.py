from django.contrib.auth.base_user import BaseUserManager
from django.utils.translation import gettext_lazy as _
from django.db import transaction


class CustomUserManager(BaseUserManager):
    """
    Custom user model manager where email is the unique identifiers
    for authentication instead of usernames.
    """
    def _create_user(self, email, password, **extra_fields):
        """
        Creates and saves a User with the given email,and password.
        """
    
        if not email:
            raise ValueError('The given email must be set')
        try:
            with transaction.atomic():
                email = email.lower()
                user = self.model(email=email, **extra_fields)
                user.set_password(password)
                user.save(using=self._db)
                return user
        except:
            raise
       
    def create_user(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        return self._create_user(email, password, **extra_fields)
 
    def create_superuser(self, email, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))
        return self._create_user(email, password, **extra_fields)

"""
class AttendeeManager(BaseUserManager):
    def _create_attendee(self, email, password, **extra_fields):
        if email is None:
            raise TypeError('The given email must be set')
        try:
            with transaction.atomic():
                attendee = self.model(email=email, **extra_fields)
                attendee.set_password(password)
                attendee.save(using=self._db)
                return attendee
        except:
            raise
    def create_attendee(self, email, password=None, **extra_fields):
        #extra_fields.setdefault('is_attendee', True)
        #if extra_fields.get('is_attendee') is not True:
            #raise ValueError(_('Attendee must have is_attendee=True.'))
        return self._create_attendee(email, password, **extra_fields)

"""

