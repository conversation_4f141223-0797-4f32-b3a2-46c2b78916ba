from django.db import models
import uuid 
from django.contrib.auth.models import AbstractBaseUser
from django.contrib.auth.models import PermissionsMixin
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from .managers import CustomUserManager

def user_directory_path(instance, filename):
    # file will be uploaded to MEDIA_ROOT/avatar_<id>/<filename>
    return 'avatar_{0}/{1}'.format(instance.id, filename)

class CustomUser(AbstractBaseUser, PermissionsMixin):
    id = models.UUIDField(primary_key = True, default = uuid.uuid4, editable = False)
    email = models.EmailField(_('email address'), unique=True)
    first_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100,blank=True, null=True)
    screen_name = models.CharField(max_length=100, blank=True, null=True)
    avatar = models.ImageField(upload_to=user_directory_path,blank=True, null=True )
    avatar_schema = models.CharField(max_length=200, blank=True, null=True)

    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_admin = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    objects = CustomUserManager()

    class Meta:
        # Add related_name to resolve reverse accessor conflicts
        swappable = 'AUTH_USER_MODEL'

    def save(self, *args, **kwargs):
        self.email = self.email.strip().lower()
        super(CustomUser, self).save(*args, **kwargs)
        return self
 
    def get_full_name(self):
        return f'{self.first_name} {self.last_name}'
 
    def get_short_name(self):
        return self.first_name
 
    def natural_key(self):
        return (self.first_name, self.last_name)
 
    def __str__(self):
        return self.email


# Create your models here.
