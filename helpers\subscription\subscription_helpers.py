from subscriptions.models import Subscription
from django.contrib.auth import get_user_model
from helpers.tenants.tenant_helpers import get_current_tenant
from custom_fields.models import Text<PERSON>ield, CustomField, ProfileField
from rest_framework.response import Response
from rest_framework import status

User = get_user_model()


def get_subscriber(user):
    try:
        subscriber = Subscription.objects.get(user=user)
    except Exception:
        subscriber = False
    return subscriber


def get_user(email):
    try:
        return User.objects.get(email=email)
    except User.DoesNotExist:
        return False


def get_user_sso(email, first_name, last_name):
    try:
        return User.objects.get(email=email, first_name=first_name, last_name=last_name)
    except User.DoesNotExist:
        return False


def get_or_create_user(email, first_name, last_name, created_users, modified_users):
    user = get_user(email)
    if user:
        user.first_name = first_name
        user.last_name = last_name
        modified_users.append(email)
    else:
        user = User.objects.create(
            email=email, first_name=first_name, last_name=last_name)
        created_users.append(email)
    user.save()
    return user


def get_password(row, user):
    event = get_current_tenant()
    if 'password' in row.keys() and row['password'] != '':
        password = row['password']
        user.set_password(password)
    elif event.event_password:
        user.set_password(event.event_password)
    elif not user.password:
        return False
    user.save()
    return user


def get_custom_field(name):
    try:
        field = TextField.objects.get(name=name)
        try:
            return CustomField.objects.get(id=field.fk.id)
        except CustomField.DoesNotExist:
            res = {'error': 'custom field not found'}
            return Response(res, status=status.HTTP_404_NOT_FOUND)
    except TextField.DoesNotExist:
        res = {'error': 'text field not found'}
        return Response(res, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return False

def get_profile_fields(user, custom_field):
    try:
        return ProfileField.objects.get(user=user, custom_field=custom_field)
    except Exception:
        return False

