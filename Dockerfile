# ================================================================================
# PRODUCTION-MATCHING DOCKERFILE
# Uses Python 3.12 to match Elastic Beanstalk AL2023 v4.6.0 production environment
# ================================================================================

# Use Python 3.12 to match EB AL2023 v4.6.0 production
FROM python:3.12-slim

# Install system dependencies including PostgreSQL development files
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    postgresql-client \
    pkg-config \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    DJANGO_SETTINGS_MODULE=backend.settings \
    DJANGO_DEBUG=False

# Copy requirements first to leverage Docker cache
COPY requirements.txt .

# Install build dependencies first
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir pycparser>=2.21 cffi>=1.17.1 Cython>=3.0.10

# Install Python dependencies with retry logic and increased timeout
RUN pip install --no-cache-dir --use-pep517 --timeout 100 --retries 3 -r requirements.txt

# Create necessary directories
RUN mkdir -p /app/media /app/staticfiles

# Copy the rest of the application
COPY . .

# Set permissions for media and static directories
RUN chmod -R 755 /app/media /app/staticfiles

# Expose port
EXPOSE 8000

# Command to run the application
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]