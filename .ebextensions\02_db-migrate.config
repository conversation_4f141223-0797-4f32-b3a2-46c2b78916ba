container_commands:
  01_collectstatic:
    command: "source /var/app/venv/*/bin/activate && python manage.py collectstatic --no-input"
    ignoreErrors: false

  02_migrate_schemas:
    command: "source /var/app/venv/*/bin/activate && python manage.py migrate_schemas"
    leader_only: true
    ignoreErrors: false

  03_create_superuser:
    command: "source /var/app/venv/*/bin/activate && python manage.py createsu"
    leader_only: true
    ignoreErrors: true  # May fail if superuser already exists

  04_migrate_tenant_domain:
    command: "source /var/app/venv/*/bin/activate && python manage.py migratetendom"
    leader_only: true
    ignoreErrors: true  # May fail if already configured

  05_create_tenant:
    command: "source /var/app/venv/*/bin/activate && python manage.py createawsten"
    leader_only: true
    ignoreErrors: true  # May fail if tenant already exists

  06_configure_wsgi:
    command: 'echo "WSGIPassAuthorization On" >> ../wsgi.conf'
    ignoreErrors: true  # May fail if already configured
  
option_settings:
  aws:elasticbeanstalk:application:environment:
    DJANGO_SETTINGS_MODULE: backend.settings