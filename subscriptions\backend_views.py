from custom_fields.models import <PERSON>Field, CustomField, TextField, SelectField, ParagraphField, IntegerField, FloatField, BoolField, CheckboxField, SelectOption, CheckboxOption
from custom_fields.serializers import ProfileFieldSerializer
from .serializers import PostSubscriptionSerializer, EventRegistrantSerializer
from .models import Subscription
from .utils import send_reg_confirm
from expohall.models import Tile, Category
from rest_framework.decorators import permission_classes, api_view
from helpers.tenants.tenant_helpers import get_current_tenant
from helpers.subscription.subscription_helpers import (
    get_subscriber,
    get_user,
    get_or_create_user,
    get_password,
    get_custom_field,
    get_profile_fields,
)
from django.db.models import Q
from tags.models import TagGroup
from spaces.models import Space
from auditorium.models import Auditorium
from logs.models import LoginEvent

from rest_framework_api_key.permissions import HasAPIKey
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import permissions, status

from django.contrib.auth import get_user_model
from django.contrib.auth.mixins import UserPassesTestMixin
from django.contrib import messages
from django.shortcuts import redirect, render
from django.views.generic import ListView
from django.http import Http404, JsonResponse
from django.db.models import Value
from django.db.models.functions import Concat
import io, csv 

#from django.core.urlresolvers import reverse_lazy

User = get_user_model()


def base(request):
    user = request.user
    if request.user.is_authenticated:
        if user.is_superuser:
            return render(request, 'home.html')
        try:
            subscriber = get_subscriber(user)
            role_types = subscriber.role_type
            for role_type in role_types:
                if role_type == 3:
                    return redirect('registration_dashboard')
                elif role_type == 4:
                    return redirect('zoom_listing')
            # if subscriber.role_type == [3]:
            #     return redirect('registration_dashboard')
            # elif subscriber.role_type == [4]:
            #     return redirect('zoom_listing')
        except Exception:
            return redirect('subscribe_user')

    return redirect('login_client')


class ExternalRegistration(APIView):
    permission_classes = (HasAPIKey,)

    def get_user(self, email):
        try:
            return User.objects.get(email=email)
        except User.DoesNotExist:
            return False

    def get_or_create_user(self, email, first_name, last_name):
        user = self.get_user(email)
        if user:
            user.first_name = first_name
            user.last_name = last_name
        else:
            user = User.objects.create(
                email=email, first_name=first_name, last_name=last_name)
        user.save()
        return user

    def get_password(self, request, user):
        event = request.tenant
        if 'password' in request.data.keys():
            password = request.data['password']
            user.set_password(password)
        elif event:
            if event.event_password:
                user.set_password(event.event_password)
        else:
            return False
        user.save()
        return user

    def get_custom_field(self, name):
        try:
            # Try to find the field in all field types
            field_types = [
                (SelectField, 'select_field'),
                (CheckboxField, 'checkbox_field'),
                (TextField, 'text_field'),
                (ParagraphField, 'paragraph_field'),
                (IntegerField, 'integer_field'),
                (FloatField, 'float_field'),
                (BoolField, 'boolean_field')
            ]
            
            for field_class, related_name in field_types:
                try:
                    field = field_class.objects.get(name=name)
                    return CustomField.objects.get(id=field.fk.id)
                except field_class.DoesNotExist:
                    continue
            
            print(f'No custom field found with name: {name}')
            return None

        except Exception as e:
            print(f'Error in get_custom_field: {str(e)}')
            return None

    def validate_select_value(self, custom_field, value):
        try:
            # Get the SelectField associated with this CustomField
            select_field = SelectField.objects.get(fk=custom_field)
            # Get all valid options for this field
            valid_options = [option.value for option in select_field.options.all()]
            
            # If value is a list, check if all values are valid
            if isinstance(value, list):
                return all(v in valid_options for v in value)
            # If value is a single value, check if it's valid
            return value in valid_options
            
        except SelectField.DoesNotExist:
            return False

    def get_profile_fields(self, user, custom_field):
        try:
            return ProfileField.objects.get(user=user, custom_field=custom_field)
        except ProfileField.DoesNotExist:
            return False

    def get(self, request, format=None, **kwargs):
        subscriptions = Subscription.objects.all()
        serializer = EventRegistrantSerializer(subscriptions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        try:
            email = request.data['email']
            email = email.lower()
            if not email:
                raise Http404
            first_name = request.data['first_name']
            if not first_name:
                raise Http404
            last_name = request.data['last_name']
            if not last_name:
                raise Http404
            
            if 'password' in request.data:
                password = request.data['password']
                if not password:
                    res = {'error': 'no password found'}
                    return Response(res, status=status.HTTP_400_BAD_REQUEST)
                if len(password) < 3:
                    res = {'error': 'password must be at least 3 characters'}
                    return Response(res, status=status.HTTP_400_BAD_REQUEST)

            user = self.get_or_create_user(email, first_name, last_name)
            user = self.get_password(request, user)

            for field in request.data:
                if field not in ['email', 'first_name', 'last_name', 'password']:
                    if field == 'screen_name':
                        user.screen_name = request.data[field]
                        user.save()
                    else:
                        custom = self.get_custom_field(field)
                        if not custom:
                            res = {'error': f"custom field '{field}' not found"}
                            return Response(res, status=status.HTTP_400_BAD_REQUEST)
                        profile_field = self.get_profile_fields(user=user, custom_field=custom)
                        data = {}
                        if profile_field:
                            data['text_value'] = request.data[field]
                            serializer = ProfileFieldSerializer(
                                profile_field, data=data, partial=True)
                        else:
                            data['user'] = user.id
                            data['text_value'] = request.data[field]
                            data['custom_field'] = custom.id
                            serializer = ProfileFieldSerializer(data=data)
                        if serializer.is_valid():
                            serializer.save()
                        else:
                            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            try:
                Subscription.objects.get(user=user)
            except Subscription.DoesNotExist:
                serializer = PostSubscriptionSerializer(
                    data={'user': user.id, 'role_type': [1]})
                if serializer.is_valid():
                    serializer.save()
                else:
                    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            send_reg_confirm(request.tenant, user)
            res = {'success'}
            return Response(res, status=status.HTTP_200_OK)
        except KeyError:
            res = {'error': 'Key Error, some fields are missing. Please check your data and try again'}
            return Response(res, status=status.HTTP_400_BAD_REQUEST)


@api_view(['DELETE'])
@permission_classes([HasAPIKey, ])
def delete_event_registrant(request):
    try:
        email = request.data['email']
        email = email.lower()
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        res = {'error': 'A user with this email does not exist'}
        return Response(res, status=status.HTTP_404_NOT_FOUND)
    if user:
        try:
            subscriber = Subscription.objects.get(user=user)
        except Subscription.DoesNotExist:
            res = {'error': 'A user with this email is not registered for this event'}
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
    if subscriber:
        user.profile_fields.all().delete()
        subscriber.delete()
        res = {
            'success': 'the user with this email has been de-registered from the event'}
        return Response(res)
    res = {'error': 'There was an error'}
    return Response(res, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([HasAPIKey, ])
def get_attendance(request):
    logs = LoginEvent.objects.distinct('subscriber__id')
    subs = logs.values_list('subscriber__id')
    subscriptions = Subscription.objects.filter(id__in=subs)
    serializer = EventRegistrantSerializer(subscriptions, many=True)
    return Response(serializer.data, status=status.HTTP_200_OK)


class RegistrationDashboardView(UserPassesTestMixin, ListView):

    def test_func(self):
        user = self.request.user
        if user.is_superuser:
            return True
        try:
            subscriber = Subscription.objects.get(user=user)
            role_types = subscriber.role_type
            return any(role_type == 3 for role_type in role_types)
        except Exception:
            return False

    def handle_no_permission(self):
        return redirect('login_client')

    model = User
    context_object_name = 'subscribers'
    template_name = 'users/registration_dashboard.html'
    paginate_by = 30

    def get_queryset(self):
        subs = Subscription.objects.all()
        query = self.request.GET.get('search_bar', '')
        if query:
            users = User.objects.annotate(search_name=Concat('first_name', Value(' '), 'last_name'))
            queryset = (
                (Q(email__icontains=query) | Q(first_name__icontains=query) | Q(last_name__icontains=query) | Q(search_name__icontains=query)) & Q(subscription__in=subs)
            )
            return users.filter(queryset).order_by('email')
        else:
            query = self.request.GET.get('role_type', '')
            if query:
                subs = subs.filter(role_type__icontains=query)
                queryset = (Q(subscription__in=subs))
                return User.objects.filter(queryset).order_by('email')
        return User.objects.filter(subscription__in=subs, ).order_by('email')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        event = get_current_tenant()        
        context['custom_fields'] = TextField.objects.all().order_by('name')
        context['event'] = event
        selected = self.request.GET.getlist('subscriber-checkbox', '')
        if selected:
            for subscriber_id in selected:
                # delete_subscriber(self.request, subscriber_id)
                try:
                    subscriber = Subscription.objects.get(user__id=subscriber_id)
                    subscriber.delete()
                except Subscription.DoesNotExist:
                    continue
        context['sub_count'] = Subscription.objects.all().count()
        return context


class AllowlistDashboardView(ListView):
    permission_classes = (permissions.IsAdminUser,)
    model = Subscription
    ordering = ('user__email', )
    context_object_name = 'subscribers'
    template_name = 'users/user_allow_list.html'
    paginate_by = 30

    def get_tag_group(self, group):
        try:
            return TagGroup.objects.get(name=group)
        except TagGroup.DoesNotExist:
            return JsonResponse({'error': 'invalid tag group'}, status=400)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        tags = TagGroup.objects.all()
        event = get_current_tenant()
        context['event'] = event
        context['tags'] = tags
        return context

    def get_queryset(self):
        subs = Subscription.objects.all()
        query = self.request.GET.get('q', '')
        if query:
            subs = Subscription.objects.annotate(
                search_name=Concat('user__first_name', Value(' '), 'user__last_name'))
            queryset = (Q(user__email__icontains=query) | Q(user__first_name__icontains=query) | Q(
                user__last_name__icontains=query) | Q(search_name__icontains=query))
            return subs.filter(queryset).order_by('user__email')
        else:
            query = self.request.GET.get('tags', '')
            if query:
                tag_group = self.get_tag_group(query)
                return tag_group.subscriptions.all().order_by('user__email')
        return subs.order_by('user__email')


def delete_subscriber(request, pk):
    # if (request.GET.get('remove-btn')):
    #     print('testing...')   
    try:
        user = User.objects.get(pk=pk)
    except User.DoesNotExist as e:
        raise Http404 from e
    if user:
        try:
            subscriber = Subscription.objects.get(user=user)
        except Subscription.DoesNotExist as exc:
            raise Http404 from exc
    if subscriber:
        user.profile_fields.all().delete()
        subscriber.delete()
        return redirect('registration_dashboard')
    return redirect('registration_dashboard') 

def delete_all_subscribers(request):

    if subscribers:= Subscription.objects.all():
        for subscriber in subscribers:
            subscriber.delete()
    
    return redirect('registration_dashboard')


class BatchAttendeeRegistration(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'import/import_attendee.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        csv_file = request.FILES['attendeefile']
        data_set = csv_file.read().decode('UTF-8')
        io_string = io.StringIO(data_set)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        bad_emails = []
        created_users = []
        modified_users = []
        try:
            # next(io_string)
            reader = csv.DictReader(io_string)
            for row in reader:
                email = row['email']
                email = email.lower()
                first_name = row['first_name']
                last_name = row['last_name']

                if email == '' or first_name == '' or last_name == '':
                    user = False
                else:
                    user = get_or_create_user(email, first_name, last_name, created_users, modified_users )
                    user = get_password(row, user)

                if not user:
                    bad_emails.append(email)
                    continue
                for field in row.keys():
                    if field != 'email' and field != 'first_name' and field != 'last_name' and field != 'password' and row[field] != '':
                        if field == 'screen_name':
                            user.screen_name = row[field]
                            user.save()
                        else:
                            custom = get_custom_field(field)
                            profile_field = get_profile_fields(
                                user=user, custom_field=custom)
                            data = {}
                            if profile_field:
                                data['text_value'] = row[field]
                                serializer = ProfileFieldSerializer(
                                    profile_field, data=data, partial=True)
                            else:
                                data['user'] = user.id
                                data['text_value'] = row[field]
                                data['custom_field'] = custom.id
                                serializer = ProfileFieldSerializer(data=data)
                            if serializer.is_valid():
                                serializer.save()
                            else:
                                return JsonResponse(serializer.errors, status=400)
                try:
                    sub = Subscription.objects.get(user=user)
                    if 1 not in sub.role_type:
                        sub.role_type.append(1)
                        sub.save()

                except Subscription.DoesNotExist:
                    serializer = PostSubscriptionSerializer(
                        data={'user': user.id, 'role_type': [1]})
                    if serializer.is_valid():
                        serializer.save()
                    else:
                        return JsonResponse(serializer.errors, status=400)

                except Exception:
                    bad_emails.append(email)
                    continue
            res = {
                'message': 'Import Completed!',
                'bad_emails': bad_emails,
                'created_users':created_users,
                'modified_users': modified_users
            }
        except Exception:
            res = {
                'message': 'Error: There was an error',
            }
        template_name = 'import/import_attendee.html'
        return render(request, template_name, res)


class BatchAttendantRegistration(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'import/import_attendant.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        csv_file = request.FILES['attendantfile']
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        bad_emails = []
        created_users = []
        modified_users = []
        try:
            reader = csv.DictReader(file)
            for row in reader:
                email = row['email']
                email = email.lower()
                first_name = row['first_name']
                last_name = row['last_name']

                if email == '' or first_name == '' or last_name == '':
                    user = False
                else:
                    user = get_or_create_user(email, first_name, last_name, created_users, modified_users)
                    user = get_password(row, user)
                if not user:
                    bad_emails.append(email)
                    continue
                for field in row.keys():
                    if field != 'email' and field != 'first_name' and field != 'last_name' and field != 'password' and row[field] != '':
                        if field == 'screen_name':
                            user.screen_name = row[field]
                            user.save()
                        else:
                            custom = get_custom_field(field)
                            profile_field = get_profile_fields(
                                user=user, custom_field=custom)
                            data = {}
                            if profile_field:
                                data['text_value'] = row[field]
                                serializer = ProfileFieldSerializer(
                                    profile_field, data=data, partial=True)
                            else:
                                data['user'] = user.id
                                data['text_value'] = row[field]
                                data['custom_field'] = custom.id
                                serializer = ProfileFieldSerializer(data=data)
                            if serializer.is_valid():
                                serializer.save()
                            else:
                                return JsonResponse(serializer.errors, status=400)
                try:
                    sub=Subscription.objects.get(user=user)
                    if 2 not in sub.role_type:
                        sub.role_type.append(2)
                        sub.save()
                except Subscription.DoesNotExist:
                    serializer = PostSubscriptionSerializer(
                        data={'user': user.id, 'role_type': [2]})
                    if serializer.is_valid():
                        serializer.save()
                    else:
                        return JsonResponse(serializer.errors, status=400)
                except Exception:
                    bad_emails.append(email)
                    continue
            res = {
                'message': 'Import Completed!',
                'bad_emails': bad_emails,
                'created_users':created_users,
                'modified_users': modified_users
            }
        except Exception:
            res = {
                'message': 'Error: There was an error',
            }
        template_name = 'import/import_attendant.html'
        return render(request, template_name, res)


class UploadTileAllowlist(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get_tile(self, tile):
        try:
            return Tile.objects.get(name=tile)
        except Exception:
            return False

    def get(self, request):
        template_name = 'import/import_tile_allow_list.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        csv_file = request.FILES['attendeefile']
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        try:
            bad_emails = []
            bad_tiles = []
            reader = csv.DictReader(file)
            for row in reader:
                email = row['email']
                email = email.lower()
                tile_name = row['tile']
                user = get_user(email)
                if user:
                    subscriber = get_subscriber(user)
                    tile = self.get_tile(tile_name)
                    if subscriber:
                        if tile:
                            if subscriber not in tile.allow_list.all():
                                tile.allow_list.add(subscriber)
                                tile.save()
                        else:
                            bad_tiles.append(tile_name)
                else:
                    bad_emails.append(email)

            res = {
                'message': 'Success!',
                'bad_emails': bad_emails,
                'bad_tiles': bad_tiles
            }
        except Exception:
            res = {'message': 'Error: There was an error'}
        template_name = 'import/import_tile_allow_list.html'
        return render(request, template_name, res)


class UploadSpaceAllowlist(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get_space(self, space_name):
        try:
            return Space.objects.get(name=space_name)
        except Exception:
            return False

    def get(self, request):
        template_name = 'import/import_space_allow_list.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        csv_file = request.FILES['attendeefile']
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        try:
            bad_emails = []
            bad_spaces = []
            reader = csv.DictReader(file)
            for row in reader:
                email = row['email']
                email = email.lower()
                space_name = row['space']
                user = get_user(email)
                if user:
                    subscriber = get_subscriber(user)
                    space = self.get_space(space_name)
                    if subscriber:
                        if space:
                            if subscriber not in space.allow_list.all():
                                space.allow_list.add(subscriber)
                                space.save()
                        else:
                            bad_spaces.append(space_name)
                else:
                    bad_emails.append(email)

            res = {
                'message': 'Success!',
                'bad_emails': bad_emails,
                'bad_spaces': bad_spaces
            }
        except Exception:
            res = {'message': 'Error: There was an error'}
        template_name = 'import/import_space_allow_list.html'
        return render(request, template_name, res)


class UploadCategoryAllowlist(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get_category(self, category_name):
        try:
            return Category.objects.get(name=category_name)
        except Exception:
            return False

    def get(self, request):
        template_name = 'import/import_category_allow_list.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        csv_file = request.FILES['attendeefile']
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        try:
            bad_emails = []
            bad_categories = []
            reader = csv.DictReader(file)
            for row in reader:
                email = row['email']
                email = email.lower()
                category_name = row['category']
                user = get_user(email)
                if user:
                    subscriber = get_subscriber(user)
                    category = self.get_category(category_name)
                    if subscriber:
                        if category:
                            if subscriber not in category.allow_list.all():
                                category.allow_list.add(subscriber)
                                category.save()
                        else:
                            bad_categories.append(category_name)
                else:
                    bad_emails.append(email)

            res = {
                'message': 'Success!',
                'bad_emails': bad_emails,
                'bad_categories': bad_categories
            }
        except Exception:
            res = {'message': 'Error: There was an error'}
        template_name = 'import/import_category_allow_list.html'
        return render(request, template_name, res)


class UploadAuditoriumAllowlist(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get_auditorium(self, auditorium_name):
        try:
            return Auditorium.objects.get(name=auditorium_name)
        except Exception:
            return False

    def get(self, request):
        template_name = 'import/import_auditorium_allow_list.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        csv_file = request.FILES['attendeefile']
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        try:
            bad_emails = []
            bad_auditoriums = []
            reader = csv.DictReader(file)
            for row in reader:
                email = row['email']
                email = email.lower()
                auditorium_name = row['auditorium']
                user = get_user(email)
                if user:
                    subscriber = get_subscriber(user)
                    auditorium = self.get_auditorium(auditorium_name)
                    if subscriber:
                        if auditorium:
                            if subscriber not in auditorium.allow_list.all():
                                auditorium.allow_list.add(subscriber)
                                auditorium.save()
                        else:
                            bad_auditoriums.append(auditorium_name)
                else:
                    bad_emails.append(email)

            res = {
                'message': 'Success!',
                'bad_emails': bad_emails,
                'bad_auditoriums': bad_auditoriums
            }
        except Exception:
            res = {'message': 'Error: There was an error'}
        template_name = 'import/import_auditorium_allow_list.html'
        return render(request, template_name, res)


class UploadTagGroups(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get_tag_group(self, tag_name):
        try:
            return TagGroup.objects.get(name=tag_name)
        except Exception:
            return False

    def get(self, request):
        template_name = 'import/import_tag_groups.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        csv_file = request.FILES['attendeefile']
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        try:
            bad_emails = []
            bad_tags = []
            reader = csv.DictReader(file)
            for row in reader:
                email = row['email']
                email = email.lower()
                tag_name = row['tag']
                user = get_user(email)
                if user:
                    subscriber = get_subscriber(user)
                    tag = self.get_tag_group(tag_name)
                    if subscriber:
                        if tag:
                            if subscriber not in tag.subscriptions.all():
                                tag.subscriptions.add(subscriber)
                                tag.save()
                        else:
                            bad_tags.append(tag_name)
                else:
                    bad_emails.append(email)

            res = {
                'message': 'Success!',
                'bad_emails': bad_emails,
                'bad_tags': bad_tags
            }
        except Exception:
            res = {'message': 'Error: There was an error'}
        template_name = 'import/import_tag_groups.html'
        return render(request, template_name, res)


class SubscribeSingleUser(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        template_name = 'users/event_password.html'
        return render(request, template_name)

    def post(self, request):
        template_name = 'users/event_password.html'
        try:
            role_type = request.data['role_type']
            user = request.user
            if user.email == '<EMAIL>':
                res = {
                    'message': 'Error: You cannot register this account for an event'}
                return render(request, template_name, res)

            subscriber = get_subscriber(user)
            if not subscriber:
                serializer = PostSubscriptionSerializer(
                    data={'user': user.id, 'role_type': [role_type]})
                if serializer.is_valid():
                    serializer.save()
                else:
                    return JsonResponse(serializer.errors, status=400)
            res = {'message': 'Success!'}
            return render(request, template_name, res)
        except Exception:
            res = {'message': 'Error: There was an error'}
            return render(request, template_name, res)


class RegisterSingleUser(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'users/register_single_user.html'
        return render(request, template_name)

    def post(self, request):
        template_name = 'users/register_single_user.html'
        try:
            email = request.data['email']
            email = email.lower()
            password = request.data['password']
            first_name = request.data['first_name']
            last_name = request.data['last_name']
            screen_name = request.data['screen_name']
            role_type = request.data['role_type']
            try:
                user = User.objects.create(
                    email=email, first_name=first_name, last_name=last_name, screen_name=screen_name)
                user.set_password(password)
                user.save()
                serializer = PostSubscriptionSerializer(
                    data={'user': user.id, 'role_type': [role_type]})
                if serializer.is_valid():
                    serializer.save()

                else:
                    return JsonResponse(serializer.errors, status=400)
                res = {'message': 'Success!'}
                return render(request, template_name, res)

            except Exception:
                res = {
                    'message': 'Error: User already exists, use the user register to register this user'}
                return render(request, template_name, res)

        except Exception:
            res = {'message': 'Error: There was an error'}
            return render(request, template_name, res)


class SubscribeAnotherUser(APIView):
    permission_classes = (permissions.IsAuthenticated,)

    def get(self, request):
        template_name = 'users/subscribe_another.html'
        return render(request, template_name)

    def post(self, request):
        template_name = 'users/subscribe_another.html'
        try:
            email = request.data['email'].lower()
            role_type = request.data['role_type']
            if email == '<EMAIL>':
                res = {
                    'message': 'Error: You cannot register this account for an event'}
                return render(request, template_name, res)
            user = get_user(email)
            if not user:
                res = {
                    'message': 'Error: There is no user associated with this email, please use the new registrant menu to register this user'}
                return render(request, template_name, res)

            subscriber = get_subscriber(user)
            if not subscriber:
                serializer = PostSubscriptionSerializer(
                    data={'user': user.id, 'role_type': [role_type]})
                if serializer.is_valid():
                    serializer.save()
                else:
                    return JsonResponse(serializer.errors, status=400)
            res = {'message': 'Success!'}
            return render(request, template_name, res)
        except KeyError:
            res = {'message': 'Error: There was an error'}
            return render(request, template_name, res)


class BatchSubscriptionCreation(APIView):
    permission_classes = (permissions.IsAdminUser,)
    
    def get_user(self, email):
        try:
            return User.objects.get(email=email)
        except User.DoesNotExist:
            return False
    
    def get_or_create_user(self, email, first_name, last_name, created_users, modified_users):
        user = self.get_user(email)
        if not first_name:
            if user:
                first_name = user.first_name
            else:
                first_name = email.split('@')[0]
                
        if user:
            user.first_name = first_name
            user.last_name = last_name
            modified_users.append(email)
        else:
            user = User.objects.create(
                email=email, first_name=first_name, last_name=last_name)
            created_users.append(email)
            
        user.save()
        return user

    def get_custom_field(self, name):
        try:
            # Try to find the field in all field types
            field_types = [
                (SelectField, 'select_field'),
                (CheckboxField, 'checkbox_field'),
                (TextField, 'text_field'),
                (ParagraphField, 'paragraph_field'),
                (IntegerField, 'integer_field'),
                (FloatField, 'float_field'),
                (BoolField, 'boolean_field')
            ]
            
            for field_class, related_name in field_types:
                try:
                    field = field_class.objects.get(name=name)
                    return CustomField.objects.get(id=field.fk.id)
                except field_class.DoesNotExist:
                    continue
            
            print(f'No custom field found with name: {name}')
            return None

        except Exception as e:
            print(f'Error in get_custom_field: {str(e)}')
            return None

    def validate_field_value(self, custom_field, value):
        try:
            # Check SelectField
            try:
                select_field = SelectField.objects.get(fk=custom_field)
                valid_options = [option.value for option in select_field.options.all()]
                if isinstance(value, list):
                    return all(v in valid_options for v in value)
                return value in valid_options
            except SelectField.DoesNotExist:
                pass

            # Check CheckboxField
            try:
                checkbox_field = CheckboxField.objects.get(fk=custom_field)
                valid_options = [option.value for option in checkbox_field.options.all()]
                if isinstance(value, list):
                    return all(v in valid_options for v in value)
                return value in valid_options
            except CheckboxField.DoesNotExist:
                pass

            # Check IntegerField
            try:
                integer_field = IntegerField.objects.get(fk=custom_field)
                try:
                    int_value = int(value)
                    if integer_field.minimum is not None and int_value < integer_field.minimum:
                        return False
                    if integer_field.maximum is not None and int_value > integer_field.maximum:
                        return False
                    return True
                except (ValueError, TypeError):
                    return False
            except IntegerField.DoesNotExist:
                pass

            # Check FloatField
            try:
                float_field = FloatField.objects.get(fk=custom_field)
                try:
                    float_value = float(value)
                    if float_field.minimum is not None and float_value < float_field.minimum:
                        return False
                    if float_field.maximum is not None and float_value > float_field.maximum:
                        return False
                    return True
                except (ValueError, TypeError):
                    return False
            except FloatField.DoesNotExist:
                pass

            # Check BoolField
            try:
                BoolField.objects.get(fk=custom_field)
                if isinstance(value, bool):
                    return True
                if isinstance(value, str):
                    return value.lower() in ('true', 'false', 'yes', 'no', '1', '0')
                return False
            except BoolField.DoesNotExist:
                pass

            # TextField and ParagraphField don't need validation
            return True

        except Exception as e:
            print(f'Error in validate_field_value: {str(e)}')
            return False

    def get_field_type(self, custom_field):
        try:
            if SelectField.objects.filter(fk=custom_field).exists():
                return 'select'
            if CheckboxField.objects.filter(fk=custom_field).exists():
                return 'checkbox'
            if TextField.objects.filter(fk=custom_field).exists():
                return 'text'
            if ParagraphField.objects.filter(fk=custom_field).exists():
                return 'paragraph'
            if IntegerField.objects.filter(fk=custom_field).exists():
                return 'integer'
            if FloatField.objects.filter(fk=custom_field).exists():
                return 'float'
            if BoolField.objects.filter(fk=custom_field).exists():
                return 'boolean'
            return 'text'  # default to text
        except Exception:
            return 'text'

    def prepare_field_value(self, field_type, value):
        try:
            if field_type == 'select':
                return value[0] if isinstance(value, list) else value
            elif field_type == 'checkbox':
                return value if isinstance(value, list) else [value]
            elif field_type == 'integer':
                return int(value[0]) if isinstance(value, list) else int(value)
            elif field_type == 'float':
                return float(value[0]) if isinstance(value, list) else float(value)
            elif field_type == 'boolean':
                if isinstance(value, bool):
                    return value
                if isinstance(value, str):
                    return value.lower() in ('true', 'yes', '1')
                return bool(value)
            else:  # text, paragraph
                return str(value[0]) if isinstance(value, list) else str(value)
        except Exception as e:
            print(f'Error preparing field value: {str(e)}')
            return None

    def post(self, request, format=None, **kwargs):
        subscriptions_data = request.data.get('subscriptions', [])
        if not subscriptions_data:
            return Response({'error': 'No subscriptions provided'}, status=status.HTTP_400_BAD_REQUEST)
        
        results = {
            'successful': [],
            'failed': [],
            'created_users': [],
            'modified_users': []
        }
        
        for subscription_data in subscriptions_data:
            try:
                # Extract user data from the nested structure
                user_data = subscription_data.get('user', {})
                email = user_data.get('email', '').lower()
                first_name = user_data.get('first_name', '')
                last_name = user_data.get('last_name', '')
                screen_name = user_data.get('screen_name', '')
                role_type = subscription_data.get('role_type', [1])  # Default to attendee
                
                if not email:
                    results['failed'].append({
                        'email': email,
                        'error': 'Missing required field: email'
                    })
                    continue
                
                # Get or create user with the required parameters
                user = self.get_or_create_user(
                    email, 
                    first_name, 
                    last_name, 
                    results['created_users'], 
                    results['modified_users']
                )
                
                # Update screen_name if provided
                if screen_name:
                    user.screen_name = screen_name
                    user.save()
                
                # Set password if provided
                if 'password' in user_data:
                    password = user_data['password']
                    if password and len(password) >= 3:
                        user.set_password(password)
                        user.save()
                
                # Create or update subscription
                try:
                    subscription = Subscription.objects.get(user=user)
                    # Replace role_type with the new values
                    if role_type:
                        subscription.role_type = role_type
                        subscription.save()
                except Subscription.DoesNotExist:
                    serializer = PostSubscriptionSerializer(
                        data={'user': user.id, 'role_type': role_type})
                    if serializer.is_valid():
                        serializer.save()
                    else:
                        results['failed'].append({
                            'email': email,
                            'error': serializer.errors
                        })
                        continue
                
                # Process custom fields if provided
                if 'custom_fields' in subscription_data:
                    for field_data in subscription_data['custom_fields']:
                        field_name = field_data.get('name')
                        field_value = field_data.get('value')
                        
                        if not field_name or field_value is None:
                            continue
                        
                        try:
                            # Get the custom field
                            custom_field = self.get_custom_field(field_name)
                            if not custom_field:
                                results['failed'].append({
                                    'email': email,
                                    'error': f'Custom field "{field_name}" not found'
                                })
                                continue

                            # Validate the field value
                            if not self.validate_field_value(custom_field, field_value):
                                results['failed'].append({
                                    'email': email,
                                    'error': f'Invalid value for field "{field_name}"'
                                })
                                continue

                            # Get the field type and prepare the value
                            field_type = self.get_field_type(custom_field)
                            prepared_value = self.prepare_field_value(field_type, field_value)
                            
                            if prepared_value is None:
                                results['failed'].append({
                                    'email': email,
                                    'error': f'Could not prepare value for field "{field_name}"'
                                })
                                continue

                            # Get or create profile field
                            profile_field = get_profile_fields(user=user, custom_field=custom_field)
                            
                            # Prepare data for the profile field
                            data = {
                                'user': user.id,
                                'custom_field': custom_field.id
                            }

                            # Set the appropriate value based on field type
                            if field_type == 'select':
                                data['select_value'] = prepared_value
                            elif field_type == 'checkbox':
                                data['checkbox_value'] = prepared_value
                            elif field_type == 'integer':
                                data['integer_value'] = prepared_value
                            elif field_type == 'float':
                                data['float_value'] = prepared_value
                            elif field_type == 'boolean':
                                data['boolean_value'] = prepared_value
                            elif field_type == 'paragraph':
                                data['paragraph_value'] = prepared_value
                            else:  # text
                                data['text_value'] = prepared_value
                            
                            # Update or create profile field
                            if profile_field:
                                serializer = ProfileFieldSerializer(profile_field, data=data, partial=True)
                            else:
                                serializer = ProfileFieldSerializer(data=data)
                            
                            if serializer.is_valid():
                                serializer.save()
                            else:
                                results['failed'].append({
                                    'email': email,
                                    'error': f'Error saving custom field "{field_name}": {serializer.errors}'
                                })
                                
                        except Exception as e:
                            results['failed'].append({
                                'email': email,
                                'error': f'Error processing custom field "{field_name}": {str(e)}'
                            })
                            continue
                
                # Send registration confirmation if requested
                if subscription_data.get('send_confirmation', False):
                    try:
                        send_reg_confirm(request.tenant, user)
                    except Exception as e:
                        print(f'Error sending confirmation email: {str(e)}')
                
                results['successful'].append({
                    'email': email,
                    'user_id': str(user.id),
                    'subscription_id': str(Subscription.objects.get(user=user).id)
                })
                
            except Exception as e:
                results['failed'].append({
                    'email': user_data.get('email', 'unknown'),
                    'error': str(e)
                })
        
        return Response(results, status=status.HTTP_200_OK)
