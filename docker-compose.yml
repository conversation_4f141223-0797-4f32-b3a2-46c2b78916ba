# ================================================================================
# DOCKER COMPOSE CONFIGURATION
# Local development environment that mirrors Elastic Beanstalk AL2023 production
# ================================================================================
#
# USAGE:
#    docker-compose up
#    - Available at http://localhost:8002
#    - Uses Python 3.12 (same as EB AL2023 v4.6.0)
#    - Perfect replica of production environment
#
# ================================================================================

version: "3"

services:
  redis:
    image: redis:latest
    expose:
      - "6379"
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=expo
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    expose:
      - "5432"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
   
  web:
    build: .
    command: >
      bash -c "
        echo 'Waiting for database...' &&
        while ! pg_isready -h db -p 5432 -U postgres; do
          sleep 2
        done &&
        echo 'Database is ready!' &&
        python manage.py collectstatic --noinput &&
        python manage.py makemigrations --noinput &&
        python manage.py migrate_schemas --shared &&
        python manage.py migrate_schemas --tenant &&
        python manage.py createten &&
        python manage.py createsu &&
        python manage.py runserver 0.0.0.0:8002
      "
    container_name: backend2
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    ports:
      - "8002:8002"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DJANGO_SETTINGS_MODULE=backend.settings
      - DEBUG=true
      - SECRET_KEY=your-secret-key-here
      - DATABASE_URL=************************************/expo
      - REDIS_URL=redis://redis:6379/0
      # Override .env file database settings for Docker
      - RDS_DB_NAME=expo
      - RDS_USERNAME=postgres
      - RDS_PASSWORD=postgres
      - RDS_HOSTNAME=db
      - RDS_PORT=5432
    links:
      - db
      - redis
    logging:
      options:
        max-size: 50m



volumes:
  postgres_data:
  static_volume:
  media_volume: