* {
    font-family: 'Open Sans', Helvetica, Arial, sans-serif;
}
body {
    display: flex;
    justify-content: center;
    font-weight: 700;
    background: #00253A;
    font-family: 'Open Sans', Helvetica, Arial, sans-serif;
    max-width: 95%;
    margin-left: auto;
    margin-right: auto;
}

table {
    background-color: #fff;
    border-radius: 7px;
    border: solid 0.5px rgb(29 59 77);
    border-collapse: collapse;
    border-spacing: 0;
    font: normal 13px Arial, sans-serif;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    margin-top: 2rem;
    max-width: 90%;
}
th {
    background-color: rgb(29 59 77);
    color: #fff;
    padding: 10px;
    text-align: left;
}
td {
    border-bottom: solid 1px rgb(29 59 77);
    color: #333;
    padding: 10px;
    transition: all ease 0.3s;
}
.align-left{
    text-align: left;
}
.align-middle{
    text-align: center;
}
tr:nth-child(odd) {
    background: #f5f5f5;
}
tr:hover td {
    background: #555;
    color: #fff;
}
tr:hover td I {
    color: #fff;
}
h2 {
    color: #fff;
    text-align: center;
}
.card {
    max-width: 1200px;
    margin-top: 20px;
    cursor: initial;
    margin-bottom:20px;
}
.navigation{
    display: flex;
    justify-content: space-between;
}
/* Consider giving this Logout link a class or hardcoding the style */
.top-button, .link-button {
    margin-top: 2rem;
    margin-bottom: 2rem;
}
.top-button >div>a {
    padding: 5px 20px;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    border: 2px #25bfff solid;
    border-radius: 20px;
    cursor: pointer;
    text-decoration: none;
}
.link-button  >div>a {
    padding: 5px 20px;
    color: #00253a;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    border: 2px #00253a solid;
    border-radius: 20px;
    cursor: pointer;
    text-decoration: none;
}
.link-button i{
    /* color: #fff; */
    font-size: 1rem;

}
.top-button>div>a:hover, 
.card > .export-button >div>a:hover, 
button:hover, button:hover>i
{
    color:rgba(255,255,255,0.1);
}
.link-button>div>a:hover,
.link-button>div>a:hover>i{
    color: rgba(0,0,0,0.25);

}
.action-items{
    
    background-color: #C0E2F5;
    color: #00253a;
    border: 1px solid #00253a;
    padding: 0.5rem 1rem 0rem 1rem;
    margin: 1rem;
    margin-left: 2rem;

}
h3{
    margin:0;
    color: #00253a;
}
.action-card{
    display: flex;
    justify-content: space-evenly;
    flex-direction: row;
    flex-wrap: wrap;

}
i {
    font-size: 1.5rem;
    color: rgb(29 59 77);
}

.icon-link:hover i{
    color: rgb(29 59 77);
}
.pagination {
    display: flex;
    flex-wrap: wrap;
    border: 1px #25bfff solid;
    border-radius: 20px;
    padding: 5px 20px;
    cursor: pointer;
    text-decoration: none;
    margin: 10px;
}
.messages p{
    color: white;
    font-family: 'Open Sans', Arial;
}

.pagination a {
    color: #CDCDCD;
    float: left;
    letter-spacing: 1px;
    padding: 8px 16px;
    text-decoration: none;
    
}
.pagination > .arrow {
    color: #25bfff;
    
}
.pagination span {
    color: black;
    float: left;
    padding: 8px 16px;
    text-decoration: none;
    
}
.pagination > .active {
    color: white;
    border: 1px #25bfff solid;
    border-radius: 20px;
    float: left;
    padding: 8px 16px;
    text-decoration: none;
}