tinymce.addI18n("vi",{"Redo":"L\xe0m l\u1ea1i","Undo":"H\u1ee7y thao t\xe1c","Cut":"C\u1eaft","Copy":"Sao ch\xe9p 2","Paste":"D\xe1n","Select all":"Ch\u1ecdn t\u1ea5t c\u1ea3","New document":"T\u1ea1o t\xe0i li\u1ec7u m\u1edbi","Ok":"\u0110\u1ed3ng \xfd","Cancel":"Hu\u1ef7 B\u1ecf","Visual aids":"Ch\u1ec9 d\u1eabn tr\u1ef1c quan","Bold":"In \u0111\u1eadm","Italic":"In nghi\xeang","Underline":"G\u1ea1ch d\u01b0\u1edbi","Strikethrough":"G\u1ea1ch ngang","Superscript":"K\xfd t\u1ef1 m\u0169","Subscript":"K\xfd t\u1ef1 th\u1ea5p","Clear formatting":"Xo\xe1 \u0111\u1ecbnh d\u1ea1ng","Remove":"Xo\xe1","Align left":"Canh tr\xe1i","Align center":"C\u0103n gi\u1eefa","Align right":"C\u0103n ph\u1ea3i","No alignment":"Kh\xf4ng c\u0103n l\u1ec1","Justify":"C\u0103n \u0111\u1ec1u hai b\xean","Bullet list":"Danh s\xe1ch d\u1ea1ng bi\u1ec3u t\u01b0\u1ee3ng","Numbered list":"Danh s\xe1ch \u0111\xe1nh s\u1ed1","Decrease indent":"Th\u1ee5t l\xf9i d\xf2ng","Increase indent":"T\u0103ng kho\u1ea3ng c\xe1ch d\xf2ng","Close":"\u0110\xf3ng L\u1ea1i","Formats":"\u0110\u1ecbnh d\u1ea1ng","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"Tr\xecnh duy\u1ec7t c\u1ee7a b\u1ea1n kh\xf4ng h\u1ed7 tr\u1ee3 truy c\u1eadp truy c\u1eadp b\u1ed9 nh\u1edb \u1ea3o, vui l\xf2ng s\u1eed d\u1ee5ng c\xe1c t\u1ed5 h\u1ee3p ph\xedm Ctrl + X, C, V.","Headings":"\u0110\u1ec1 m\u1ee5c","Heading 1":"H1","Heading 2":"H2","Heading 3":"H3","Heading 4":"H4","Heading 5":"H5","Heading 6":"H6","Preformatted":"\u0110\u1ecbnh d\u1ea1ng s\u1eb5n","Div":"Khung","Pre":"Ti\u1ec1n t\u1ed1","Code":"M\xe3","Paragraph":"\u0110o\u1ea1n v\u0103n","Blockquote":"\u0110o\u1ea1n Tr\xedch D\u1eabn","Inline":"C\xf9ng d\xf2ng","Blocks":"Kh\u1ed1i","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"D\xe1n \u0111ang trong tr\u1ea1ng th\xe1i v\u0103n b\u1ea3n thu\u1ea7n. N\u1ed9i dung s\u1ebd \u0111\u01b0\u1ee3c d\xe1n d\u01b0\u1edbi d\u1ea1ng v\u0103n b\u1ea3n thu\u1ea7n, kh\xf4ng \u0111\u1ecbnh d\u1ea1ng.","Fonts":"Ph\xf4ng ch\u1eef","Font sizes":"K\xedch th\u01b0\u1edbc ch\u1eef","Class":"L\u1edbp","Browse for an image":"Ch\u1ecdn m\u1ed9t h\xecnh \u1ea3nh","OR":"HO\u1eb6C","Drop an image here":"Th\u1ea3 h\xecnh \u1ea3nh v\xe0o \u0111\xe2y","Upload":"T\u1ea3i l\xean","Uploading image":"\u0110ang t\u1ea3i \u1ea3nh l\xean","Block":"Kh\u1ed1i","Align":"Canh l\u1ec1","Default":"M\u1eb7c \u0111\u1ecbnh","Circle":"H\xecnh tr\xf2n","Disc":"\u0110\u0129a","Square":"\xd4 vu\xf4ng","Lower Alpha":"K\xfd t\u1ef1 th\u01b0\u1eddng","Lower Greek":"S\u1ed1 Hy L\u1ea1p th\u01b0\u1eddng","Lower Roman":"S\u1ed1 la m\xe3 th\u01b0\u1eddng","Upper Alpha":"In hoa","Upper Roman":"S\u1ed1 la m\xe3 hoa","Anchor...":"Neo...","Anchor":"Neo li\xean k\u1ebft","Name":"T\xean","ID":"ID","ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.":"ID ph\u1ea3i b\u1eaft \u0111\u1ea7u b\u1eb1ng ch\u1eef c\xe1i, theo sau l\xe0 c\xe1c ch\u1eef c\xe1i, s\u1ed1, d\u1ea5u g\u1ea1ch ngang, d\u1ea5u ch\u1ea5m, d\u1ea5u hai ch\u1ea5m ho\u1eb7c d\u1ea5u g\u1ea1ch d\u01b0\u1edbi.","You have unsaved changes are you sure you want to navigate away?":"B\u1ea1n ch\u01b0a l\u01b0u thay \u0111\u1ed5i b\u1ea1n c\xf3 ch\u1eafc b\u1ea1n mu\u1ed1n di chuy\u1ec3n \u0111i?","Restore last draft":"Kh\xf4i ph\u1ee5c b\u1ea3n g\u1ea7n nh\u1ea5t","Special character...":"K\xfd t\u1ef1 \u0111\u1eb7c bi\u1ec7t...","Special Character":"K\xfd t\u1ef1 \u0111\u1eb7c bi\u1ec7t","Source code":"M\xe3 ngu\u1ed3n","Insert/Edit code sample":"Ch\xe8n/S\u1eeda m\xe3 m\u1eabu","Language":"Ng\xf4n ng\u1eef","Code sample...":"M\xe3 m\u1eabu...","Left to right":"Tr\xe1i sang ph\u1ea3i","Right to left":"Ph\u1ea3i sang tr\xe1i","Title":"Ti\xeau \u0111\u1ec1","Fullscreen":"To\xe0n m\xe0n h\xecnh","Action":"H\xe0nh \u0111\u1ed9ng","Shortcut":"Ph\xedm t\u1eaft","Help":"Tr\u1ee3 gi\xfap","Address":"\u0110\u1ecba ch\u1ec9","Focus to menubar":"T\u1eadp trung v\xe0o tr\xecnh \u0111\u01a1n","Focus to toolbar":"T\u1eadp trung v\xe0o thanh c\xf4ng c\u1ee5","Focus to element path":"T\u1eadp trung v\xe0o \u0111\u01b0\u1eddng d\u1eabn ph\u1ea7n t\u1eed","Focus to contextual toolbar":"T\u1eadp trung v\xe0o thanh c\xf4ng c\u1ee5 ng\u1eef c\u1ea3nh","Insert link (if link plugin activated)":"Ch\xe8n li\xean k\u1ebft","Save (if save plugin activated)":"L\u01b0u","Find (if searchreplace plugin activated)":"T\xecm ki\u1ebfm","Plugins installed ({0}):":"Plugin \u0111\xe3 c\xe0i ({0}):","Premium plugins:":"Plugin cao c\u1ea5p:","Learn more...":"T\xecm hi\u1ec3u th\xeam...","You are using {0}":"B\u1ea1n \u0111ang s\u1eed d\u1ee5ng {0}","Plugins":"Plugin","Handy Shortcuts":"Ph\xedm t\u1eaft th\xf4ng d\u1ee5ng","Horizontal line":"K\u1ebb ngang","Insert/edit image":"Ch\xe8n/s\u1eeda \u1ea3nh","Alternative description":"M\xf4 t\u1ea3 thay th\u1ebf (Alt)","Accessibility":"Kh\u1ea3 n\u0103ng ti\u1ebfp c\u1eadn","Image is decorative":"H\xecnh \u1ea3nh minh ho\u1ea1","Source":"Ngu\u1ed3n","Dimensions":"K\xedch th\u01b0\u1edbc","Constrain proportions":"T\u1ef7 l\u1ec7 r\xe0ng bu\u1ed9c","General":"Chung","Advanced":"N\xe2ng cao","Style":"Ki\u1ec3u","Vertical space":"N\u1eb1m d\u1ecdc","Horizontal space":"N\u1eb1m ngang","Border":"Vi\u1ec1n","Insert image":"Ch\xe8n \u1ea3nh","Image...":"H\xecnh \u1ea3nh...","Image list":"Danh s\xe1ch h\xecnh \u1ea3nh","Resize":"Thay \u0111\u1ed5i k\xedch th\u01b0\u1edbc","Insert date/time":"Ch\xe8n ng\xe0y/th\xe1ng","Date/time":"Ng\xe0y/th\u1eddi gian","Insert/edit link":"Ch\xe8n/s\u1eeda li\xean k\u1ebft","Text to display":"N\u1ed9i dung hi\u1ec3n th\u1ecb","Url":"Url","Open link in...":"M\u1edf \u0111\u01b0\u1eddng d\u1eabn trong...","Current window":"C\u1eeda s\u1ed5 hi\u1ec7n t\u1ea1i","None":"Kh\xf4ng","New window":"C\u1eeda s\u1ed5 m\u1edbi","Open link":"M\u1edf li\xean k\u1ebft","Remove link":"H\u1ee7y li\xean k\u1ebft","Anchors":"Neo","Link...":"Li\xean k\u1ebft...","Paste or type a link":"D\xe1n ho\u1eb7c nh\u1eadp m\u1ed9t li\xean k\u1ebft","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":"\u0110\u1ecba ch\u1ec9 URL b\u1ea1n v\u1eeba nh\u1eadp gi\u1ed1ng nh\u01b0 m\u1ed9t \u0111\u1ecba ch\u1ec9 email. B\u1ea1n c\xf3 mu\u1ed1n th\xeam ti\u1ec1n t\u1ed1 mailto: kh\xf4ng?","The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":"\u0110\u1ecba ch\u1ec9 URL b\u1ea1n v\u1eeba nh\u1eadp gi\u1ed1ng nh\u01b0 m\u1ed9t li\xean k\u1ebft. B\u1ea1n c\xf3 mu\u1ed1n th\xeam ti\u1ec1n t\u1ed1 http:// kh\xf4ng?","The URL you entered seems to be an external link. Do you want to add the required https:// prefix?":"Li\xean k\u1ebft b\u1ea1n nh\u1eadp c\xf3 v\u1ebb l\xe0 li\xean k\u1ebft b\xean ngo\xe0i. B\u1ea1n c\xf3 mu\u1ed1n b\u1eaft bu\u1ed9c th\xeam ti\u1ec1n t\u1ed1 https:// ?","Link list":"Danh s\xe1ch li\xean k\u1ebft","Insert video":"Ch\xe8n video","Insert/edit video":"Ch\xe8n/s\u1eeda video","Insert/edit media":"Ch\xe8n/s\u1eeda \u0111a ph\u01b0\u01a1ng ti\u1ec7n","Alternative source":"Ngu\u1ed3n thay th\u1ebf","Alternative source URL":"\u0110\u01b0\u1eddng d\u1eabn ngu\u1ed3n thay th\u1ebf","Media poster (Image URL)":"\xc1p ph\xedch \u0111a ph\u01b0\u01a1ng ti\u1ec7n (\u0110\u01b0\u1eddng d\u1eabn h\xecnh \u1ea3nh)","Paste your embed code below:":"D\xe1n m\xe3 nh\xfang c\u1ee7a b\u1ea1n d\u01b0\u1edbi \u0111\xe2y:","Embed":"Nh\xfang","Media...":"\u0110a ph\u01b0\u01a1ng ti\u1ec7n...","Nonbreaking space":"Kh\xf4ng xu\u1ed1ng h\xe0ng","Page break":"Ng\u1eaft trang","Paste as text":"D\xe1n \u0111o\u1ea1n v\u0103n b\u1ea3n","Preview":"Xem th\u1eed","Print":"In","Print...":"In...","Save":"L\u01b0u","Find":"T\xecm ki\u1ebfm","Replace with":"Thay th\u1ebf b\u1edfi","Replace":"Thay th\u1ebf","Replace all":"Thay t\u1ea5t c\u1ea3","Previous":"Tr\u01b0\u1edbc","Next":"Sau","Find and Replace":"T\xecm v\xe0 thay th\u1ebf","Find and replace...":"T\xecm v\xe0 thay th\u1ebf...","Could not find the specified string.":"Kh\xf4ng t\xecm th\u1ea5y chu\u1ed7i ch\u1ec9 \u0111\u1ecbnh.","Match case":"Ph\xe2n bi\u1ec7t hoa/th\u01b0\u1eddng","Find whole words only":"Ch\u1ec9 t\xecm to\xe0n b\u1ed9 t\u1eeb","Find in selection":"T\xecm trong l\u1ef1a ch\u1ecdn","Insert table":"Th\xeam b\u1ea3ng","Table properties":"Thu\u1ed9c t\xednh b\u1ea3ng","Delete table":"Xo\xe1 b\u1ea3ng","Cell":"\xd4","Row":"D\xf2ng","Column":"C\u1ed9t","Cell properties":"Thu\u1ed9c t\xednh \xf4","Merge cells":"Tr\u1ed9n \xf4","Split cell":"Chia c\u1eaft \xf4","Insert row before":"Th\xeam d\xf2ng ph\xeda tr\xean","Insert row after":"Th\xeam d\xf2ng ph\xeda d\u01b0\u1edbi","Delete row":"Xo\xe1 d\xf2ng","Row properties":"Thu\u1ed9c t\xednh d\xf2ng","Cut row":"C\u1eaft d\xf2ng","Cut column":"C\u1eaft c\u1ed9t","Copy row":"Sao ch\xe9p d\xf2ng","Copy column":"Sao ch\xe9p c\u1ed9t","Paste row before":"D\xe1n v\xe0o ph\xeda tr\u01b0\u1edbc, tr\xean","Paste column before":"D\xe1n c\u1ed9t v\xe0o b\xean tr\xe1i","Paste row after":"D\xe1n v\xe0o ph\xeda sau, d\u01b0\u1edbi","Paste column after":"D\xe1n c\u1ed9t v\xe0o b\xean ph\u1ea3i","Insert column before":"Th\xeam c\u1ed9t b\xean tr\xe1i","Insert column after":"Th\xeam c\u1ed9t b\xean ph\u1ea3i","Delete column":"Xo\xe1 c\u1ed9t","Cols":"C\u1ed9t","Rows":"D\xf2ng","Width":"\u0110\u1ed9 R\u1ed9ng","Height":"\u0110\u1ed9 Cao","Cell spacing":"Kho\u1ea3ng c\xe1ch \xf4","Cell padding":"Kho\u1ea3ng c\xe1ch trong \xf4","Row clipboard actions":"H\xe0ng thao t\xe1c tr\xean khay nh\u1edb t\u1ea1m","Column clipboard actions":"C\u1ed9t thao t\xe1c tr\xean khay nh\u1edb t\u1ea1m","Table styles":"Ki\u1ec3u d\xe1ng b\u1ea3ng","Cell styles":"Ki\u1ec3u d\xe1ng \xf4","Column header":"Ti\xeau \u0111\u1ec1 c\u1ed9t","Row header":"Ti\xeau \u0111\u1ec1 h\xe0ng","Table caption":"Ch\xfa th\xedch b\u1ea3ng","Caption":"Ti\xeau \u0111\u1ec1","Show caption":"Hi\u1ec7n ti\xeau \u0111\u1ec1","Left":"Tr\xe1i","Center":"Gi\u1eefa","Right":"Ph\u1ea3i","Cell type":"Lo\u1ea1i \xf4","Scope":"Quy\u1ec1n","Alignment":"Canh ch\u1ec9nh","Horizontal align":"C\u0103n ngang","Vertical align":"C\u0103n d\u1ecdc","Top":"Tr\xean","Middle":"\u1ede gi\u1eefa","Bottom":"D\u01b0\u1edbi","Header cell":"Ti\xeau \u0111\u1ec1 \xf4","Row group":"Gom nh\xf3m d\xf2ng","Column group":"Gom nh\xf3m c\u1ed9t","Row type":"Th\u1ec3 lo\u1ea1i d\xf2ng","Header":"Ti\xeau \u0111\u1ec1","Body":"N\u1ed9i dung","Footer":"Ch\xe2n","Border color":"M\xe0u vi\u1ec1n","Solid":"N\xe9t li\u1ec1n m\u1ea1ch","Dotted":"N\xe9t ch\u1ea5m","Dashed":"N\xe9t \u0111\u1ee9t","Double":"N\xe9t \u0111\xf4i","Groove":"3D c\xf3 x\u1ebb r\xe3nh","Ridge":"3D tr\xf2n n\u1ed5i","Inset":"3D khung ch\xecm","Outset":"3D khung n\u1ed5i","Hidden":"\u1ea8n","Insert template...":"Th\xeam m\u1eabu...","Templates":"M\u1eabu","Template":"M\u1eabu","Insert Template":"Th\xeam m\u1eabu","Text color":"M\xe0u v\u0103n b\u1ea3n","Background color":"M\xe0u n\u1ec1n","Custom...":"Tu\u1ef3 ch\u1ec9nh...","Custom color":"Tu\u1ef3 ch\u1ec9nh m\xe0u","No color":"Kh\xf4ng c\xf3 m\xe0u","Remove color":"B\u1ecf m\xe0u","Show blocks":"Hi\u1ec3n th\u1ecb kh\u1ed1i","Show invisible characters":"Hi\u1ec3n th\u1ecb k\xfd t\u1ef1 \u1ea9n","Word count":"S\u1ed1 t\u1eeb","Count":"\u0110\u1ebfm","Document":"T\xe0i li\u1ec7u","Selection":"L\u1ef1a ch\u1ecdn","Words":"Ch\u1eef","Words: {0}":"Ch\u1eef: {0}","{0} words":"{0} ch\u1eef","File":"T\u1eadp tin","Edit":"S\u1eeda","Insert":"Ch\xe8n","View":"Xem","Format":"\u0110\u1ecbnh d\u1ea1ng","Table":"B\u1ea3ng","Tools":"C\xf4ng c\u1ee5","Powered by {0}":"Cung c\u1ea5p b\u1edfi {0}","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"Rich Text Area. B\u1ea5m ALT-F9 m\u1edf tr\xecnh \u0111\u01a1n. B\u1ea5m ALT-F10 m\u1edf thanh c\xf4ng c\u1ee5. B\u1ea5m ALT-0 m\u1edf tr\u1ee3 gi\xfap","Image title":"Ti\xeau \u0111\u1ec1 \u1ea3nh","Border width":"\u0110\u1ed9 d\xe0y vi\u1ec1n","Border style":"Ki\u1ec3u vi\u1ec1n","Error":"L\u1ed7i","Warn":"C\u1ea3nh b\xe1o","Valid":"H\u1ee3p l\u1ec7","To open the popup, press Shift+Enter":"\u0110\u1ec3 m\u1edf h\u1ed9p tho\u1ea1i, nh\u1ea5n Shift+Enter","Rich Text Area":"V\xf9ng v\u0103n b\u1ea3n phong ph\xfa","Rich Text Area. Press ALT-0 for help.":"V\xf9ng v\u0103n b\u1ea3n phong ph\xfa. Nh\xe2n ALT-0 \u0111\u1ec3 bi\u1ebft th\xeam.","System Font":"Ph\xf4ng ch\u1eef h\u1ec7 th\u1ed1ng","Failed to upload image: {0}":"Kh\xf4ng th\u1ec3 \u0111\u0103ng h\xecnh: {0}","Failed to load plugin: {0} from url {1}":"Kh\xf4ng th\u1ec3 t\u1ea3i plugin: {0} t\u1eeb \u0111\u01b0\u1eddng d\u1eabn {1}","Failed to load plugin url: {0}":"Kh\xf4ng th\u1ec3 t\u1ea3i \u0111\u01b0\u1eddng d\u1eabn plugin: {0}","Failed to initialize plugin: {0}":"Kh\xf4ng th\u1ec3 kh\u1edfi t\u1ea1o plugin: {0}","example":"v\xed d\u1ee5","Search":"T\xecm ki\u1ebfm","All":"T\u1ea5t c\u1ea3","Currency":"Ti\u1ec1n t\u1ec7","Text":"V\u0103n b\u1ea3n","Quotations":"Tr\xedch d\u1eabn","Mathematical":"To\xe1n h\u1ecdc","Extended Latin":"Latin m\u1edf r\u1ed9ng","Symbols":"K\xfd hi\u1ec7u","Arrows":"M\u0169i t\xean","User Defined":"\u0110\u1ecbnh ngh\u0129a b\u1edfi ng\u01b0\u1eddi d\xf9ng","dollar sign":"k\xfd hi\u1ec7u \u0111\xf4 la","currency sign":"k\xfd hi\u1ec7u ti\u1ec1n t\u1ec7","euro-currency sign":"k\xfd hi\u1ec7u euro","colon sign":"d\u1ea5u hai ch\u1ea5m","cruzeiro sign":"k\xfd hi\u1ec7u cruzeiro","french franc sign":"k\xfd hi\u1ec7u franc Ph\xe1p","lira sign":"k\xfd hi\u1ec7u lira","mill sign":"k\xfd hi\u1ec7u mill","naira sign":"k\xfd hi\u1ec7u naira","peseta sign":"k\xfd hi\u1ec7u peseta","rupee sign":"k\xfd hi\u1ec7u rupee","won sign":"k\xfd hi\u1ec7u won","new sheqel sign":"k\xfd hi\u1ec7u new sheqel","dong sign":"k\xfd hi\u1ec7u \u0111\u1ed3ng","kip sign":"k\xfd hi\u1ec7u \u0111\u1ed3ng kip","tugrik sign":"k\xfd hi\u1ec7u tugrik","drachma sign":"k\xfd hi\u1ec7u drachma","german penny symbol":"k\xfd hi\u1ec7u xu \u0110\u1ee9c","peso sign":"k\xfd hi\u1ec7u peso","guarani sign":"k\xfd hi\u1ec7u guarani","austral sign":"k\xfd hi\u1ec7u austral","hryvnia sign":"k\xfd hi\u1ec7u hryvnia","cedi sign":"k\xfd hi\u1ec7u cedi ","livre tournois sign":"k\xfd hi\u1ec7u livre tournois","spesmilo sign":"k\xfd hi\u1ec7u spesmilo","tenge sign":"K\xfd hi\u1ec7u tenge","indian rupee sign":"k\xfd hi\u1ec7u rupee \u1ea5n \u0111\u1ed9","turkish lira sign":"k\xfd hi\u1ec7u lira th\u1ed5 nh\u0129 k\u1ef3","nordic mark sign":"k\xfd hi\u1ec7u mark b\u1eafc \xe2u","manat sign":"k\xfd hi\u1ec7u manat","ruble sign":"k\xfd hi\u1ec7u r\xfap","yen character":"k\xfd hi\u1ec7u y\xean","yuan character":"k\xfd hi\u1ec7u yuan","yuan character, in hong kong and taiwan":"k\xfd hi\u1ec7u yuan, \u1edf h\u1ed3ng k\xf4ng v\xe0 \u0111\xe0i loan","yen/yuan character variant one":"k\xfd hi\u1ec7u y\xean/yuan bi\u1ebfn th\u1ec3","Emojis":"Bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\xfac","Emojis...":"Bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\xfac...","Loading emojis...":"\u0110ang t\u1ea3i bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\xfac...","Could not load emojis":"Kh\xf4ng th\u1ec3 t\u1ea3i bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\xfac","People":"Ng\u01b0\u1eddi","Animals and Nature":"\u0110\u1ed9ng v\u1eadt v\xe0 thi\xean nhi\xean","Food and Drink":"Th\u1ee9c \u0103n v\xe0 \u0111\u1ed3 u\u1ed1ng","Activity":"Ho\u1ea1t \u0111\u1ed9ng","Travel and Places":"Du l\u1ecbch v\xe0 \u0111\u1ecba \u0111i\u1ec3m","Objects":"V\u1eadt d\u1ee5ng","Flags":"C\u1edd","Characters":"Nh\xe2n v\u1eadt","Characters (no spaces)":"K\xfd t\u1ef1 (kh\xf4ng kho\u1ea3ng tr\u1ed1ng)","{0} characters":"{0} k\xfd t\u1ef1","Error: Form submit field collision.":"L\u1ed7i: Xung \u0111\u1ed9t tr\u01b0\u1eddng trong bi\u1ec3u m\u1eabu.","Error: No form element found.":"L\u1ed7i: Kh\xf4ng t\xecm th\u1ea5y bi\u1ec3u m\u1eabu.","Color swatch":"M\u1eabu m\xe0u","Color Picker":"B\u1ea3ng ch\u1ecdn m\xe0u","Invalid hex color code: {0}":"M\xe3 m\xe0u hex kh\xf4ng h\u1ee3p l\u1ec7: {0}","Invalid input":"\u0110\u1ea7u v\xe0o kh\xf4ng h\u1ee3p l\u1ec7","R":"M\xe0u \u0111\u1ecf","Red component":"Th\xe0nh ph\u1ea7n \u0111\u1ecf","G":"M\xe0u xanh l\xe1","Green component":"Th\xe0nh ph\u1ea7n xanh","B":"M\xe0u xanh d\u01b0\u01a1ng","Blue component":"Th\xe0nh ph\u1ea7n xanh","#":"#","Hex color code":"M\xe3 m\xe0u hex","Range 0 to 255":"T\u1eeb 0 \u0111\u1ebfn 255","Turquoise":"Ng\u1ecdc lam","Green":"Xanh l\xe1","Blue":"Xanh d\u01b0\u01a1ng","Purple":"T\xedm","Navy Blue":"Xanh n\u01b0\u1edbc bi\u1ec3n","Dark Turquoise":"Ng\u1ecdc lam t\u1ed1i","Dark Green":"Xanh l\xe1 c\xe2y \u0111\u1eadm","Medium Blue":"Xanh d\u01b0\u01a1ng nh\u1eb9","Medium Purple":"T\xedm nh\u1eb9","Midnight Blue":"Xanh d\u01b0\u01a1ng n\u1eeda \u0111\xeam","Yellow":"V\xe0ng","Orange":"Cam","Red":"\u0110\u1ecf","Light Gray":"X\xe1m nh\u1ea1t","Gray":"X\xe1m","Dark Yellow":"V\xe0ng \u0111\u1eadm","Dark Orange":"Cam \u0111\u1eadm","Dark Red":"\u0110\u1ecf \u0111\u1eadm","Medium Gray":"X\xe1m nh\u1eb9","Dark Gray":"X\xe1m \u0111\u1eadm","Light Green":"Xanh l\xe1 nh\u1ea1t","Light Yellow":"V\xe0ng nh\u1ea1t","Light Red":"\u0110\u1ecf nh\u1ea1t","Light Purple":"T\xedm nh\u1ea1t","Light Blue":"Xanh d\u01b0\u01a1ng nh\u1ea1t","Dark Purple":"T\xedm \u0111\u1eadm","Dark Blue":"Xanh d\u01b0\u01a1ng \u0111\u1eadm","Black":"\u0110en","White":"Tr\u1eafng","Switch to or from fullscreen mode":"Chuy\u1ec3n qua ho\u1eb7c l\u1ea1i ch\u1ebf \u0111\u1ed9 to\xe0n m\xe0n h\xecnh","Open help dialog":"M\u1edf h\u1ed9p tho\u1ea1i tr\u1ee3 gi\xfap","history":"l\u1ecbch s\u1eed","styles":"ki\u1ec3u","formatting":"\u0111\u1ecbnh d\u1ea1ng","alignment":"canh l\u1ec1","indentation":"th\u1ee5t \u0111\u1ea7u d\xf2ng","Font":"Ph\xf4ng ch\u1eef","Size":"K\xedch th\u01b0\u1edbc","More...":"Th\xeam...","Select...":"Ch\u1ecdn...","Preferences":"T\xf9y ch\u1ecdn","Yes":"C\xf3","No":"Kh\xf4ng","Keyboard Navigation":"Ph\xedm \u0111i\u1ec1u h\u01b0\u1edbng","Version":"Phi\xean b\u1ea3n","Code view":"Xem code","Open popup menu for split buttons":"M\u1edf menu b\u1eadt l\xean cho c\xe1c n\xfat t\xe1ch","List Properties":"Thu\u1ed9c t\xednh danh s\xe1ch","List properties...":"C\xe1c thu\u1ed9c t\xednh danh s\xe1ch...","Start list at number":"Danh s\xe1ch b\u1eaft \u0111\u1ea7u b\u1eb1ng s\u1ed1","Line height":"\u0110\u1ed9 cao d\xf2ng","Dropped file type is not supported":"Lo\u1ea1i t\u1ec7p \u0111\xe3 k\xe9o th\u1ea3 kh\xf4ng \u0111\u01b0\u1ee3c h\u1ed7 tr\u1ee3","Loading...":"\u0110ang t\u1ea3i...","ImageProxy HTTP error: Rejected request":"L\u1ed7i HTTP ImageProxy: Y\xeau c\u1ea7u b\u1ecb t\u1eeb ch\u1ed1i","ImageProxy HTTP error: Could not find Image Proxy":"L\u1ed7i HTTP ImageProxy: Kh\xf4ng th\u1ec3 t\xecm th\u1ea5y Image Proxy","ImageProxy HTTP error: Incorrect Image Proxy URL":"L\u1ed7i HTTP ImageProxy: URL proxy h\xecnh \u1ea3nh kh\xf4ng ch\xednh x\xe1c","ImageProxy HTTP error: Unknown ImageProxy error":"L\u1ed7i HTTP ImageProxy: L\u1ed7i ImageProxy kh\xf4ng x\xe1c \u0111\u1ecbnh"});