.top-button {
    display: flex;
    justify-content: space-between;
}

.back-btn, .media-btn {
    padding: 5px 20px;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    border: 2px #25bfff solid;
    border-radius: 20px;
    cursor: pointer;
    text-decoration: none;
}

.back-btn:hover, .media-btn:hover {
    color: rgba(255, 255, 255, 0.5)
}

.header-container {
    display: flex;
    flex-direction: column;
    text-align: center;
}

.new-image-btn-container {
    display: block;
    text-align: center;
}

.select-tile, .select-banner, .select-auditorium {
    display: flex;
    flex-direction: column;
    margin: 0 1rem;
}

.modal-body {
    margin: auto;
}

.file-input {
    margin-top: 1rem;
}

.image-list {
    display: grid;
    grid-template-columns: auto auto auto;
    grid-gap: 1rem;
    margin-top: 2rem;
    margin-right: auto;
    margin-left: auto;
}

.content {
    position: relative;
    cursor: pointer;
}

.content .content-overlay {
    background: rgba(0,0,0,0.7);
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    opacity: 0;
    -webkit-transition: all 0.4s ease-in-out 0s;
    -moz-transition: all 0.4s ease-in-out 0s;
    transition: all 0.4s ease-in-out 0s;
}

.content:hover .content-overlay{
    opacity: 1;
}

.content-image{
    width: 255px;
}

.content-details {
    position: absolute;
    text-align: center;
    top: 50%;
    left: 50%;
    opacity: 0;
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}

.content:hover .content-details{
    top: 50%;
    left: 50%;
    opacity: 1;
}

.fadeIn-bottom{
    top: 80%;
}

.modal {
    color: black;
}

.checked {
    display: none;
}