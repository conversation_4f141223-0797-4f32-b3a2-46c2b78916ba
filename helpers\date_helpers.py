import re
import datetime
from django.utils import timezone
from auditorium.serializers import AuditoriumScheduleSerializer

def convert_offset(offset):
    if not offset:
        return None
    sign, hours, minutes = re.match(r'([+\-]?)(\d{2}):(\d{2})', offset).groups()
    sign = -1 if sign == '-' else 1
    hours, minutes = int(hours), int(minutes)
    tzinfo = datetime.timezone(sign * datetime.timedelta(hours=hours, minutes=minutes))
    return tzinfo

def create_datetime(d, t, tzinfo):
    new_date = datetime.datetime.combine(d, t)
    new_date =  new_date.replace(tzinfo=tzinfo)
    return new_date.isoformat()
    

def days_between(d1, d2):
    return abs((d2 - d1).days)

def return_aud_dates(ordered_auds, offset):
    tzinfo = convert_offset(offset)

    dates = []
    # initialize with a small date
    last_date = datetime.date(2002, 2, 2)
    for aud in ordered_auds:

        start = aud.start_date_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=tzinfo)
        end = aud.end_date_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=tzinfo)
        # if the date wasnt already added
        if start.date() > last_date:
            # need to add ranges between start and end, leave off at end
            if start.date() != end.date():
                num_days = days_between(
                    start.date(), end.date())
                for x in range(0, num_days+1):
                    dates.append((start +
                                 datetime.timedelta(days=x)).date())

                last_date = end.date()
            # start and end same, add the date cause it wasnt added
            else:
                last_date = start.date()
                dates.append(start.date())
        # if the end date of the last session was before end date of last, need to add remaining day(s)
        elif end.date() > last_date:
            # if different, add multiple days
            if start.date() != end.date():
                num_days = days_between(last_date, end.date())
                for x in range(1, num_days+1):
                    dates.append(last_date +
                                 datetime.timedelta(days=x))

                last_date = end.date()
            # if not, just add the end
            else:
                last_date = end.date()
                dates.append(end.date())
    return dates

def return_day_auds(ordered_auds, date, offset):
    tzinfo = convert_offset(offset)
    data = []
    for aud in ordered_auds:
        start = aud.start_date_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=tzinfo)
        end = aud.end_date_time.replace(tzinfo=datetime.timezone.utc).astimezone(tz=tzinfo)
        # we are out of range: either starts after the date
        if start.date() > date:
            break
        # we are out of range: ends before the date
        elif end.date() < date:
            continue

        # start date is less than or equal to date, end time is greater than date
        #we know we are in the range
        else:
            serializer = AuditoriumScheduleSerializer(aud)
            aud_data = serializer.data
            aud_data['session_end_date_time'] = aud.end_date_time
            aud_data['session_start_date_time'] = aud.start_date_time
            if start.date() != end.date():
                if start.date() == date:
                    t = datetime.time(23, 59)
                    new_end = create_datetime(date, t, tzinfo)
                    aud_data['end_date_time'] = new_end
                    aud_data['start_date_time'] = start
                    data.append(aud_data)
                elif end.date() == date:
                    t = datetime.time(00, 00)
                    new_start = create_datetime(date, t, tzinfo)
                    aud_data['start_date_time'] = new_start
                    aud_data['end_date_time'] = end
                    data.append(aud_data)
                #start < date < end  
                else:
                    t1 = datetime.time(00, 00)
                    t2 = datetime.time(23, 59)
                    new_start = create_datetime(date, t1, tzinfo)
                    new_end = create_datetime(date, t2, tzinfo)
                    aud_data['start_date_time'] = new_start
                    aud_data['end_date_time'] = new_end
                    data.append(aud_data)
            #we know start < date < end, and that start = end => start = date = end 
            else:
                aud_data['start_date_time'] = start
                aud_data['end_date_time'] = end
                data.append(aud_data)
    return data


def get_now_datetime():
    return datetime.datetime.now()
    # return timezone.now()