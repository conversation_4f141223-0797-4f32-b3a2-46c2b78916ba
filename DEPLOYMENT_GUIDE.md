# Elastic Beanstalk Deployment Guide

## Platform Version Configuration

This project is configured to use **Python 3.12 on Amazon Linux 2023 v4.6.0** consistently across all environments.

## Platform Version Lock

### Why Lock Platform Version?
- **Prevents deployment surprises** from automatic platform updates
- **Ensures consistency** between local development, CI/CD, and production
- **Maintains compatibility** with tested configurations
- **Provides predictable deployments** every time

### Current Configuration
- **Platform**: `64bit Amazon Linux 2023 v4.6.0 running Python 3.12`
- **Python Version**: 3.12 (matches local Docker environment)
- **Auto-updates**: Disabled to prevent compatibility issues

## Configuration Files

### 1. `.elasticbeanstalk/config.yml`
```yaml
global:
  default_platform: 64bit Amazon Linux 2023 v4.6.0 running Python 3.12
  platform_version: 4.6.0
```

### 2. `.travis.yml`
All deployment branches specify:
```yaml
solution_stack_name: "64bit Amazon Linux 2023 v4.6.0 running Python 3.12"
```

### 3. `.ebextensions/00_platform.config`
Environment variable lock:
```yaml
aws:elasticbeanstalk:application:environment:
  EB_PLATFORM_VERSION: "64bit Amazon Linux 2023 v4.6.0 running Python 3.12"
```

## Deployment Commands

### Manual Deployment
```bash
# Deploy to staging (main branch)
eb deploy mbvs-server-v2-staging-updated

# Deploy to production (production branch)
eb deploy mbvs-server-prod-v2-env

# Deploy to development (dev branch)
eb deploy mbvs-server-v2-dev-updated
```

### Automated Deployment (Travis CI)
- **Development**: Push to `dev` branch
- **Staging**: Push to `main` branch  
- **Production**: Push to `production` branch

All deployments automatically use the locked platform version.

## Environment Consistency

### Local Development
```bash
# Docker environment matches production exactly
docker-compose up
# Uses Python 3.12 (same as EB AL2023 v4.6.0)
```

### CI/CD Testing
```bash
# Travis CI uses Python 3.12 for testing
python: "3.12"
```

### Production Deployment
```bash
# EB uses Python 3.12 AL2023 v4.6.0
solution_stack_name: "64bit Amazon Linux 2023 v4.6.0 running Python 3.12"
```

## Platform Version Management

### Checking Current Platform Version
```bash
# Check environment platform version
eb status

# List available platform versions
aws elasticbeanstalk list-available-solution-stacks \
  --query 'SolutionStacks[?contains(@, `Python 3.12`) && contains(@, `Amazon Linux 2023`)]'
```

### Updating Platform Version (When Ready)
1. **Test locally** with new Python version in Docker
2. **Update configuration files** with new platform version
3. **Test in development environment** first
4. **Deploy to staging** for validation
5. **Deploy to production** after thorough testing

### Emergency Platform Update
If you need to update the platform version:

1. Update `.elasticbeanstalk/config.yml`:
   ```yaml
   default_platform: 64bit Amazon Linux 2023 v4.7.0 running Python 3.12
   platform_version: 4.7.0
   ```

2. Update `.travis.yml` for all branches:
   ```yaml
   solution_stack_name: "64bit Amazon Linux 2023 v4.7.0 running Python 3.12"
   ```

3. Update `.ebextensions/00_platform.config`:
   ```yaml
   EB_PLATFORM_VERSION: "64bit Amazon Linux 2023 v4.7.0 running Python 3.12"
   ```

4. Test thoroughly before deploying to production.

## Troubleshooting

### Platform Version Mismatch
If you see platform version errors:
1. Check all configuration files use the same version string
2. Verify the platform version exists in AWS
3. Update Docker environment to match if needed

### Deployment Failures
1. Check Travis CI logs for platform version issues
2. Verify AWS credentials have permission to specify platform versions
3. Ensure the platform version string is exactly correct

### Local vs Production Differences
1. Verify Docker uses Python 3.12 (same as EB)
2. Check environment variables match between local and EB
3. Ensure package versions are identical via requirements.txt

## Benefits of This Setup

✅ **Predictable Deployments**: Same platform version every time
✅ **No Surprise Updates**: Platform won't auto-update unexpectedly  
✅ **Perfect Local Parity**: Docker exactly matches production
✅ **Consistent CI/CD**: Travis CI uses same Python version
✅ **Easy Rollbacks**: Known working platform version
✅ **Controlled Updates**: Update platform version when YOU decide
