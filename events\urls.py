from django.contrib.admin.views.decorators import staff_member_required
from django.urls import path, include, re_path
from . import views, upload_data, download_data, api_views

urlpatterns = [
    path('events/', include(([
        path('', views.EventList.as_view()),
        path('<pk>/', views.EventDetail.as_view()),
    ], 'events'), namespace='events')),
    
    path('events/from_admintools/create/', views.create_event, name='create_event_from_admintools'),

    path('events_filter/account/<str:account>/', views.EventsFilterByAccount.as_view(), name='events_filter_account'),
    path('events_filter/user_accounts/<str:user>/', views.EventsFilterByUserAccounts.as_view(), name='events_filter_user_accounts'),
    path('events_filter/user_access/<str:user>/', views.EventsFilterByUserAccess.as_view(), name='events_filter_user_access'),

    path('add_event_access/', views.add_event_access, name='add_event_access'),
    path('remove_event_access/', views.remove_event_access, name='remove_event_access'),

    path('upload/login_logo/', views.upload_login_logo),
    path('get_current_event/', views.get_current_event),
    path('get_event_from_frontend/<str:subdomain>/', views.get_event_from_frontend),

    # data upload
    path('event_upload/events_upload/', staff_member_required(upload_data.EventsImport.as_view(), login_url='/login_client/'), name='events_upload'),

    # data download
    re_path(r'event_download/events_download/$', staff_member_required(download_data.export_event_data, login_url='/login_client/'), name='export_event_data_csv'),

    # api
    path('update_event_domain_urls/', api_views.update_event_domain_urls, name='update_event_domain_urls'),
]
