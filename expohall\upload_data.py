from expohall.models import Tile
from helpers.imports.universal_helpers import get_default_false
from helpers.imports.spaces_helpers import get_spaces_by_ids, get_spaces
from helpers.imports.expohall_helpers import get_or_create_category, get_or_create_tile, get_tile_by_id, get_tile, get_or_create_resource, get_categories_by_ids
from helpers.imports.subscriptions_helpers import get_attendants
from helpers.imports.auditorium_helpers import get_or_create_aud, get_auditoriums

from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework import status
from .models import Category

from .models import Tile

from django.contrib import messages
from django.shortcuts import render
from django.core.files import File
from django.core.files.base import ContentFile

from rest_framework.views import APIView
from rest_framework import permissions
from PIL import Image
import io, csv, sys


class BatchCategoriesImport(APIView):
    permission_classes = (permissions.IsAdminUser,)
    
    def get(self, request):
        template_name = 'event_upload/import_categories.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_categories.html'
        try:
            csv_file = request.FILES['eventfile']
        except Exception:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')

        imported_categories = []
        bad_rows = []
        bad_spaces = []
        reader = csv.DictReader(file)
        for row in reader:
            space_names = row['space_names']
            category_name = row['category_name']
            show = get_default_false(row['show'])
            randomize = get_default_false(row['randomize'])
            order = row['order']
            try:
                category = get_or_create_category(category_name)
                if space_names != '':
                    space_list = space_names.split(' \\ ')    
                    new_spaces = get_spaces(space_list)
                    if new_spaces.count() != len(space_list):
                        bad_spaces.append(category)
                    category.space.set(new_spaces)
                if show not in [category.show, '']:
                    category.show = show
                if randomize not in [category.randomize, '']:
                    category.randomize = randomize
                if order not in [category.order, '']:
                    category.order = order
                category.save()

                imported_categories.append(category)

            except Exception:
                bad_rows.append(category_name)
            context = {
                'message': 'Success!',
                'imported_categories': imported_categories,
                'bad_rows': bad_rows,
                'bad_spaces': bad_spaces
            }
        return render(request, template_name, context)


class BatchTilesImport(APIView):
    permission_classes = (permissions.IsAdminUser,)
    not_registered = []

    def get(self, request):
        template_name = 'event_upload/import_tiles.html'
        return render(request, template_name)
    
    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_tiles.html'
        try:
            csv_file = request.FILES['eventfile']
        except Exception:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')

        successful_tiles = []
        bad_tiles = []
        incomplete_items = []

        maxInt = sys.maxsize
        csv.field_size_limit(maxInt)

        reader = csv.DictReader(file)
        for row in reader:
            try:
                order = row['order']
                space_names = row['space_names']
                attendants = row['attendants']
                category_names = row['category_names']
                aud_name = row['aud_name']
                tile_name = row['tile_name']
                default_screen = row['default_screen']
                html_blurb = row['html_blurb']
                video_url = row['video_url']
                meeting_toggle = row['meeting_toggle']
                business_card_toggle = row['business_card_toggle']
                chat_toggle = row['chat_toggle']
                video_call_toggle = row['video_call_toggle']
                tile_cover_title = row['tile_cover_title']
                cs_background_color = row['cs_background_color']
                cs_opacity = row['cs_opacity']
                cs_color = row['cs_color']
                ts_text_color = row['ts_text_color']
                ts_font_size = row['ts_font_size']
                ts_background_color = row['ts_background_color']
                ts_background_opacity = row['ts_background_opacity']
                ts_vertical_alignment = row['ts_vertical_alignment']
                ts_horizontal_alignment = row['ts_horizontal_alignment']

                tile = get_or_create_tile(tile_name)
                if not tile:
                    bad_tiles.append(tile_name)
                    continue
                if order not in [tile.order, '']:
                    tile.order = order

                if space_names != '':
                    space_list = space_names.split(' \\ ')
                    new_spaces = get_spaces(space_list)
                    if new_spaces.count() != len(space_list):
                        incomplete_items.append(f'{tile.name}: Spaces')
                    tile.space.set(new_spaces)

                if category_names != '':
                    categories_list = category_names.split(' \\ ')
                    new_categories = get_categories(categories_list)
                    if new_categories.count() != len(categories_list):
                        incomplete_items.append(f'{tile.name}: Categories')
                    tile.categories.set(new_categories)

                if attendants != '':
                    attendants_list = attendants.split(' \\ ')
                    new_attendants = get_attendants(attendants_list)
                    if new_attendants.count() != len(attendants_list):
                        incomplete_items.append(f'{tile.name}: Attendants')
                    tile.attendants.set(new_attendants)

                auditorium = get_or_create_aud(aud_name)
                if auditorium:
                    aud = get_auditoriums([aud_name])
                    tile.auditoriums.set(aud)

                if default_screen not in [tile.default_screen, '']:
                    tile.default_screen = default_screen
                if html_blurb not in [tile.html_blurb, '']:
                    tile.html_blurb = html_blurb
                if video_url not in [tile.video_url, '']:
                    tile.video_url = video_url
                if meeting_toggle != '':
                    tile.meeting_toggle = get_default_false(meeting_toggle)
                if business_card_toggle != '':
                    tile.business_card_toggle = get_default_false(business_card_toggle)
                if chat_toggle != '':
                    tile.chat_toggle = get_default_false(chat_toggle)
                if video_call_toggle != '':
                    tile.video_call_toggle = get_default_false(video_call_toggle)
                if tile_cover_title not in [tile.tile_cover_title, '']:
                    tile.tile_cover_title = tile_cover_title
                if  cs_background_color not in [tile.cs_background_color, '']:
                    tile.cs_background_color = cs_background_color
                if cs_opacity not in [tile.cs_opacity, '']:
                    tile.cs_opacity = cs_opacity
                if cs_color not in [tile.cs_color, '']:
                    tile.cs_color = cs_color
                if ts_text_color not in [tile.ts_text_color, '']:
                    tile.ts_text_color = ts_text_color
                if ts_font_size not in [tile.ts_font_size, '']:
                    tile.ts_font_size = ts_font_size
                if ts_background_color not in [tile.ts_background_color, '']:
                    tile.ts_background_color = ts_background_color
                if ts_background_opacity not in [tile.ts_background_opacity, '']:
                    tile.ts_background_opacity = ts_background_opacity
                if ts_vertical_alignment not in [tile.ts_vertical_alignment, '']:
                    tile.ts_vertical_alignment = ts_vertical_alignment
                if ts_horizontal_alignment not in [tile.ts_horizontal_alignment, '']:
                    tile.ts_horizontal_alignment  = ts_horizontal_alignment
                tile.save()

                successful_tiles.append(tile)

            except Exception:
                bad_tiles.append(row['tile_name'])
        context = {
                'message': 'Upload Completed!',
                'successful_tiles': successful_tiles,
                'bad_tiles': bad_tiles,
                'incomplete_items': incomplete_items,
            }
        return render(request, template_name, context)


class TileImageUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        tiles = Tile.objects.all()
        context = {
            'tile_list': tiles,
        }
        template_name = 'event_upload/import_tile_image.html'
        return render(request, template_name, context)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_tile_image.html'
        tiles = Tile.objects.all()
        tile_pic_io = io.BytesIO()
        try:
            img_file = self.request.FILES['imgfile']
        except Exception:
            context={
                'message': 'No File Selected',
                'tile_list': tiles
            }
            return render(request, template_name, context)
        tile_ids = self.request.data.getlist('tile-ids')
        for tile_id in tile_ids:
            tile = get_tile_by_id(tile_id)
            if tile:
                try:
                    image = Image.open(img_file)
                    image.save(tile_pic_io, format='png', save=False)
                    tile_pic_io.seek(0)
                    content_file = ContentFile(tile_pic_io.read())
                    file = File(content_file)
                    tile.image.save(img_file.name, file)
                except Exception:
                    context = { 
                        'message': 'There was a problem with the image file',
                        'tile_list': tiles
                    }
                    return render(request, template_name, context)
            else:
                context = { 
                    'message': 'There was a problem with the selected tile(s). Please reload and try again',
                    'tile_list': tiles
                }
                return render(request, template_name, context)
        context = {
            'message': 'Success!',
            'tile_list': tiles
        }
        return render(request, template_name, context)
   
        
class TileResourceUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'event_upload/import_tile_resources.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_tile_resources.html'
        try:
            csv_file = request.FILES['eventfile']
        except Exception:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        imported_resources = []
        errors = []
        reader = csv.DictReader(file)
        for row in reader:
            tile_name = row['tile_name']
            tab_type = row['tab_type']
            resource_name = row['resource_name']
            url = row['url']
            priority = row['priority']

            try:
                tile = get_tile(tile_name)
                resource = get_or_create_resource(resource_name, tile)

                if tab_type not in [resource.tab_type, '']:
                    resource.tab_type = tab_type
                if url not in [resource.url, '']:
                    resource.url = url
                if priority not in [resource.priority, '']:
                    resource.priority = priority
                resource.save()

                imported_resources.append(resource)

            except Exception:
                errors.append(resource_name)

            context = {
                'message': 'Upload Completed!',
                'imported_resources': imported_resources,
                'errors': errors,
            }
        return render(request, template_name, context)



@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, permissions.IsAdminUser])
def import_categories_json(request):
    data = request.data

    if not data:
        return Response({'message': 'No Data Provided'}, status=status.HTTP_400_BAD_REQUEST)

    imported_categories = []
    bad_rows = []
    bad_spaces = []

    for row in data:
        try:
            space_ids = row.get('space_ids')
            category_name = row.get('name')
            show = get_default_false(row.get('show'))
            randomize = get_default_false(row.get('randomize'))
            order = row.get('order')

            category = get_or_create_category(category_name)
            if space_ids:
                new_spaces = get_spaces_by_ids(space_ids)
                if new_spaces.count() != len(space_ids):
                    bad_spaces.append(category.id)
                category.space.set(new_spaces)
            if show not in [category.show, '']:
                category.show = show
            if randomize not in [category.randomize, '']:
                category.randomize = randomize
            if order not in [category.order, '']:
                category.order = order
            category.save()

            imported_categories.append({'id': category.id, 'name': category.name})

        except Exception as e:
            bad_rows.append(category_name)
            print(f"Error processing row {category_name}: {e}")

    response_data = {
        'message': 'Cateories Import Completed!',
        'categories': imported_categories,
        'bad_rows': bad_rows,
        'bad_spaces': bad_spaces
    }
    return Response(response_data, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, permissions.IsAdminUser])
def import_tiles_json(request):
    data = request.data

    if not data:
        return Response({'message': 'No Data Provided'}, status=status.HTTP_400_BAD_REQUEST)

    successful_tiles = []
    bad_tiles = []
    incomplete_items = []

    for row in data:
        try:
            order = row.get('order')
            space_ids = row.get('space_ids')
            category_ids = row.get('category_ids')
            tile_name = row.get('name')
            image = row.get('image')
            default_screen = row.get('default_screen')
            html_blurb = row.get('html_blurb')
            video_url = row.get('video_url')
            meeting_toggle = row.get('meeting_toggle')
            business_card_toggle = row.get('business_card_toggle')
            chat_toggle = row.get('chat_toggle')
            video_call_toggle = row.get('video_call_toggle')
            tile_cover_title = row.get('tile_cover_title')
            cs_background_color = row.get('cs_background_color')
            cs_opacity = row.get('cs_opacity')
            cs_color = row.get('cs_color')
            ts_text_color = row.get('ts_text_color')
            ts_font_size = row.get('ts_font_size')
            ts_background_color = row.get('ts_background_color')
            ts_background_opacity = row.get('ts_background_opacity')
            ts_vertical_alignment = row.get('ts_vertical_alignment')
            ts_horizontal_alignment = row.get('ts_horizontal_alignment')
            
            tile = Tile.objects.create(name=tile_name)
            if not tile:
                bad_tiles.append(tile_name)
                continue
            if order not in [tile.order, '']:
                tile.order = order

            if space_ids:
                new_spaces = get_spaces_by_ids(space_ids)
                if new_spaces.count() != len(space_ids):
                    incomplete_items.append(f'{tile.name}: Spaces')
                tile.space.set(new_spaces)

            if category_ids:
                new_categories = get_categories_by_ids(category_ids)
                if new_categories.count() != len(category_ids):
                    incomplete_items.append(f'{tile.name}: Categories')
                tile.categories.set(new_categories)

            if default_screen not in [tile.default_screen, '']:
                tile.default_screen = default_screen
            if html_blurb not in [tile.html_blurb, '']:
                tile.html_blurb = html_blurb
            if video_url not in [tile.video_url, '']:
                tile.video_url = video_url
            if image != '':
                tile.image = image
            if meeting_toggle != '':
                tile.meeting_toggle = get_default_false(meeting_toggle)
            if business_card_toggle != '':
                tile.business_card_toggle = get_default_false(business_card_toggle)
            if chat_toggle != '':
                tile.chat_toggle = get_default_false(chat_toggle)
            if video_call_toggle != '':
                tile.video_call_toggle = get_default_false(video_call_toggle)
            if tile_cover_title not in [tile.tile_cover_title, '']:
                tile.tile_cover_title = tile_cover_title
            if cs_background_color not in [tile.cs_background_color, '']:
                tile.cs_background_color = cs_background_color
            if cs_opacity not in [tile.cs_opacity, '']:
                tile.cs_opacity = cs_opacity
            if cs_color not in [tile.cs_color, '']:
                tile.cs_color = cs_color
            if ts_text_color not in [tile.ts_text_color, '']:
                tile.ts_text_color = ts_text_color
            if ts_font_size not in [tile.ts_font_size, '']:
                tile.ts_font_size = ts_font_size
            if ts_background_color not in [tile.ts_background_color, '']:
                tile.ts_background_color = ts_background_color
            if ts_background_opacity not in [tile.ts_background_opacity, '']:
                tile.ts_background_opacity = ts_background_opacity
            if ts_vertical_alignment not in [tile.ts_vertical_alignment, '']:
                tile.ts_vertical_alignment = ts_vertical_alignment
            if ts_horizontal_alignment not in [tile.ts_horizontal_alignment, '']:
                tile.ts_horizontal_alignment = ts_horizontal_alignment
            tile.save()

            successful_tiles.append({'id': tile.id, 'name': tile.name})
        except Exception as e:
            bad_tiles.append(tile_name)
            print(f"Error processing row {tile_name}: {e}")

    response_data = {
        'message': 'Tiles Import Completed!',
        'tiles': successful_tiles,
        'bad_tiles': bad_tiles,
        'incomplete_items': incomplete_items,
    }
    return Response(response_data, status=status.HTTP_200_OK)