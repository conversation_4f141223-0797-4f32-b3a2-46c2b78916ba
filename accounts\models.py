import uuid

from django.contrib.auth import get_user_model
from django.db import models
from django.utils import timezone
from django_tenants.models import TenantMixin

User = get_user_model()

ADMIN = 'ADMIN'
EDITOR = 'EDITOR'
ROLE_CHOICES = (
    (ADMIN, 'Admin'),
    (EDITOR, 'Editor'),
)

class Client(TenantMixin):
    name = models.CharField(max_length=100)
    paid_until = models.DateField()
    on_trial = models.BooleanField()
    created_on = models.DateField(auto_now_add=True)

    # default true, schema will be automatically created and synced when it is saved
    auto_create_schema = True

class Account(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    code = models.CharField(max_length=300, unique=True, blank=False, null=True, help_text="Code to represent account, must be unique")
    name = models.Char<PERSON>ield(max_length=300, unique=False, blank=False, null=True, help_text="Human readable name of account")
    created_by = models.ForeignKey(
        User, related_name="created_accounts", blank=True, null=True, on_delete=models.SET_NULL)
    time_created = models.DateTimeField(default=timezone.now)
    objects = models.Manager()

    def __str__(self):
        return f'{self.code}: {str(self.name)}'

class Access(models.Model):
    name = models.CharField(max_length=250, unique=True, blank=False, null=False, primary_key=True)
    objects = models.Manager()

    def __str__(self):
        return self.name

PENDING = 'PENDING'
ACCEPTED = 'ACCEPTED'
EXPIRED = 'EXPIRED'
STATUS_CHOICES = (
    (PENDING, 'Pending'),
    (ACCEPTED, 'Accepted'),
    (EXPIRED, 'Expired'),
)
class Invite(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    #TODO: restrict by is_staff and is_admin
    sent_by = models.ForeignKey(
        User, on_delete=models.CASCADE, blank=False, related_name='sent_invitations')
    account = models.ForeignKey(
        Account, on_delete=models.CASCADE, blank=False, related_name='invitations')
    email = models.EmailField(blank=False, null=False)
    created_at = models.DateTimeField(default=timezone.now)
    last_sent  = models.DateTimeField(blank=True, null=True)
    status = models.CharField(max_length=50, blank=False, null=False, choices=STATUS_CHOICES, default=PENDING)
    accesses = models.ManyToManyField(
        Access, related_name='invitations')
    role = models.CharField(max_length=50, blank=False, null=False, choices=ROLE_CHOICES, default=ADMIN)
    objects = models.Manager()

    def __str__(self):
        return f'{self.email} - {self.account}'

#some model that relates role, permissions, and users 

class AccessSet(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    accesses = models.ManyToManyField(
        Access, related_name="access_sets")
    role = models.CharField(max_length=50, blank=False, null=False, choices=ROLE_CHOICES, default=ADMIN)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="access_sets")
    account = models.ForeignKey(
        Account, on_delete=models.CASCADE, blank=False, related_name='access_sets')
    created_at = models.DateTimeField(default=timezone.now)
    last_modified = models.DateTimeField(auto_now=True)
    objects = models.Manager()
    
    class Meta:
        unique_together = ('user', 'account')

    def __str__(self):
        return f'{self.user} - {self.account}'
