body {
    display: flex;
    justify-content: center;
    font-weight: 700;
    background: #00253A;
    font-family: 'Open Sans', Helvetica, Arial, sans-serif;
  }
  table {
    background-color: #fff;
    border-radius: 7px;
    border: solid 0.5px rgb(29 59 77);
    border-collapse: collapse;
    border-spacing: 0;
    font: normal 13px Arial, sans-serif;
    text-align: center;
  }
  th {
    background-color: rgb(29 59 77);
    color: #fff;
    padding: 10px;
    text-align: left;
  }
  td {
    border-bottom: solid 1px rgb(29 59 77);
    color: #333;
    padding: 10px;
    transition: all ease 0.3s;
  }
  
  .align-left{
    text-align: left;
  }
  .align-middle{
    text-align: center;
  }
  tr:nth-child(odd) {
    background: #f5f5f5;
  }
    tr:hover td {
    background: #555;
    color: #fff;
  }
    tr:hover td I {
      color: #fff;
  }
  h2 {
    color: #fff;
    text-align: center;
    margin: 40px 0;
  }
  .card {
    max-width: 1200px;
    margin-top: 20px;
    cursor: initial;
    margin-bottom:20px;
  }
  .navigation{
    display: flex;
    justify-content: space-between;
  }
  /* Consider giving this Logout link a class or hardcoding the style */
 .top-button {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
  .top-button >div>a {
    padding: 5px 20px;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    border: 2px #25bfff solid;
    border-radius: 20px;
    cursor: pointer;
    text-decoration: none;
  }
  .top-button>div>a:hover{
    color:rgba(255,255,255,0.1);
  }
  .top-button i{
    color: #fff;
    font-size: 1rem;
  }
  .action-items{
    display: flex;
    justify-content: space-between;

  }
  i {
    font-size: 1.5rem;
    color: rgb(29 59 77);
  }
  .pagination {
    display: inline-block;
    background-color:#282828;
    padding: 5px 20px;
    cursor: pointer;
    text-decoration: none;
    margin: 10px;
  }

  .pagination a {
    color: #CDCDCD;
    float: left;
    letter-spacing: 1px;
    padding: 8px 16px;
    text-decoration: none;
    font-family: "Arial", sans-serif;
    
  }
  .pagination > .arrow {
    color: #25bfff;
    
  }
  .pagination span {
    color: black;
    float: left;
    padding: 8px 16px;
    text-decoration: none;
    font-family: "Arial", sans-serif;
    
  }
  .pagination > .active {
    color: white;
    border: 1px #25bfff solid;
    border-radius: 20px;
    float: left;
    padding: 8px 16px;
    text-decoration: none;
    font-family: "Arial", sans-serif;
  }
  .messages{
  color: #fff;
  text-align: left;

}
.messages a{
  color: #25bfff;

}
#meeting-name i{
  font-size: 1rem;
}

#meeting-name a:visited{
  color: black
}