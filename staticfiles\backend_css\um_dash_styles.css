* {
  font-family: 'Open Sans', Helvetica, Arial, sans-serif;
}

body {
  max-width: 95%;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  justify-content: center;
  font-weight: 700;
  background: #00253A;
}

a {
  text-decoration: none;
  color: #fff;
}

i {
  font-size: 1.5rem;
  color: rgb(29 59 77);
}

.card {
  max-width: 1200px;
  margin-top: 20px;
  cursor: initial;
  margin-bottom:20px;
}

.navigation {
  display: flex;
  justify-content: space-between;
}

.top-button > div > a {
  padding: 5px 20px;
  color: #fff;
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: capitalize;
  border: 2px #25bfff solid;
  border-radius: 20px;
  cursor: pointer;
  text-decoration: none;
}

.top-button > div > a:hover, button:hover, 
button:hover > i, button:hover > a, 
button:hover > a > i {
  color:rgba(255,255,255,0.1);
}

.top-button {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.link-button {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

h2 {
  color: #fff;
  text-align: center;
}

.action-items {
  background-color: #C0E2F5;
  color: #00253a;
  border: 1px solid #00253a;
  margin: 1rem;
  margin-left: 2rem;
  padding: 0.5rem 0;
}

.action-card {
  display: flex;
  justify-content: space-evenly;
  flex-direction: row;
  flex-wrap: wrap;

}

.link-button > div > a {
  padding: 5px 20px;
  color: #00253a;
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: capitalize;
  border: 2px #00253a solid;
  border-radius: 20px;
  cursor: pointer;
  text-decoration: none;
  font-size: .7rem;
  margin: 0 .5rem 0 .5rem;
}

.link-button i {
  font-size: 1rem;
}

.link-button > div > a:hover,
.link-button > div > a:hover > i {
  color: rgba(0,0,0,0.25);
}

#total-reg {
  color: white;
  font-weight: bold;
  margin-bottom: 0;
}

form {
  margin-left: 2rem;
}

input[type="text"] {
  width: 40%;
  padding: 8px;
  margin: 1rem 0rem ;
}

select {
  width: 40%;
  padding: 8px;
  margin: 1rem 0rem ;
}

button {
  padding: 5px 20px;
  font-family: 'Open Sans', Helvetica, Arial, sans-serif;
  background: #00253A;
  color: #fff;
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: capitalize;
  border: 1px #25bfff solid;
  border-radius: 20px;
  cursor: pointer;
  text-decoration: none;
}

.clear-button > i, .remove-button > i, 
.remove-button > a > i {
  color: #fff;
  font-size: 1rem;
}

.clear-button, .remove-button {
  vertical-align: middle;
  margin: 0.5rem;
}

table {
  background-color: #fff;
  border-radius: 7px;
  border: solid 0.5px rgb(29 59 77);
  border-collapse: collapse;
  border-spacing: 0;
  font: normal 13px Arial, sans-serif;
  text-align: center;
  margin-top: 1rem;
}

th {
  background-color: rgb(29 59 77);
  color: #fff;
  padding: 10px;
  text-align: left;
}

td {
  border-bottom: solid 1px rgb(29 59 77);
  color: #333;
  padding: 10px;
  transition: all ease 0.3s;
}

.align-left {
  text-align: left;
}

.align-middle {
  text-align: center;
}

tr:nth-child(odd) {
  background: #f5f5f5;
}

tr:hover td {
  background: #555;
  color: #fff;
}

tr:hover td I {
  color: #fff;
}

.pagination {
  display: flex;
  flex-wrap: wrap;
  border: 1px #25bfff solid;
  border-radius: 20px;
  padding: 5px 20px;
  cursor: pointer;
  text-decoration: none;
  margin: 10px;
}

.pagination a {
  color: #CDCDCD;
  float: left;
  letter-spacing: 1px;
  padding: 8px 16px;
  text-decoration: none;
}

.pagination > .arrow {
  color: #25bfff;
}

.pagination span {
  color: black;
  float: left;
  padding: 8px 16px;
  text-decoration: none;
}

.pagination > .active {
  color: white;
  border: 1px #25bfff solid;
  border-radius: 20px;
  float: left;
  padding: 8px 16px;
  text-decoration: none;
}

.selected-bottom {
  margin-top: 0.8rem;
  width: 25%;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.remove-selected {
  margin: 0 8rem;
  display: flex;
  flex-direction: column;
}