tinymce.Resource.add('tinymce.html-i18n.help-keynav.fi',
'<h1>Näppäimistönavigoinnin aloittaminen</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt><PERSON>irr<PERSON> kohdistus valikkopalkkiin</dt>\n' +
  '  <dd>Windows tai Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Siirrä kohdistus työkalupalkkiin</dt>\n' +
  '  <dd>Windows tai Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Siirrä kohdistus alatunnisteeseen</dt>\n' +
  '  <dd>Windows tai Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Siirrä kohdistus kontekstuaaliseen työkalupalkkiin</dt>\n' +
  '  <dd>Windows, Linux tai macOS: Ctrl+F9\n' +
  '</dl>\n' +
  '\n' +
  '<p>Navigointi aloitetaan ensimmäisestä käyttöliittymän kohteesta, joka joko korostetaan tai alleviivataan, jos\n' +
  '  kyseessä on Alatunniste-elementin polun ensimmäinen kohde.</p>\n' +
  '\n' +
  '<h1>Käyttöliittymän eri osien välillä navigointi</h1>\n' +
  '\n' +
  '<p>Paina <strong>sarkainnäppäintä</strong> siirtyäksesi käyttöliittymän osasta seuraavaan.</p>\n' +
  '\n' +
  '<p>Jos haluat siirtyä edelliseen käyttöliittymän osaan, paina <strong>Shift+sarkainnäppäin</strong>.</p>\n' +
  '\n' +
  '<p><strong>Sarkainnäppäin</strong> siirtää sinua näissä käyttöliittymän osissa tässä järjestyksessä:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Valikkopalkki</li>\n' +
  '  <li>Työkalupalkin ryhmät</li>\n' +
  '  <li>Sivupalkki</li>\n' +
  '  <li>Elementin polku alatunnisteessa</li>\n' +
  '  <li>Sanalaskurin vaihtopainike alatunnisteessa</li>\n' +
  '  <li>Brändäyslinkki alatunnisteessa</li>\n' +
  '  <li>Editorin koon muuttamisen kahva alatunnisteessa</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>Jos jotakin käyttöliittymän osaa ei ole, se ohitetaan.</p>\n' +
  '\n' +
  '<p>Jos kohdistus on siirretty alatunnisteeseen näppäimistönavigoinnilla eikä sivupalkkia ole näkyvissä, <strong>Shift+sarkainnäppäin</strong>\n' +
  '  siirtää kohdistuksen työkalupalkin ensimmäiseen ryhmään, eikä viimeiseen.</p>\n' +
  '\n' +
  '<h1>Käyttöliittymän eri osien sisällä navigointi</h1>\n' +
  '\n' +
  '<p>Paina <strong>nuolinäppäimiä</strong> siirtyäksesi käyttöliittymäelementistä seuraavaan.</p>\n' +
  '\n' +
  '<p><strong>Vasen</strong>- ja <strong>Oikea</strong>-nuolinäppäimet</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>siirtävät sinua valikkopalkin valikoiden välillä.</li>\n' +
  '  <li>avaavat valikon alavalikon.</li>\n' +
  '  <li>siirtävät sinua työkalupalkin ryhmän painikkeiden välillä.</li>\n' +
  '  <li>siirtävät sinua kohteiden välillä alatunnisteen elementin polussa.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Alas</strong>- ja <strong>Ylös</strong>-nuolinäppäimet</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>siirtävät sinua valikon valikkokohteiden välillä.</li>\n' +
  '  <li>siirtävät sinua työkalupalkin ponnahdusvalikon kohteiden välillä.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Nuolinäppäimet</strong> siirtävät sinua käyttöliittymän korostetun osan sisällä syklissä.</p>\n' +
  '\n' +
  '<p>Paina <strong>Esc</strong>-näppäintä sulkeaksesi avoimen valikon, avataksesi alavalikon tai avataksesi ponnahdusvalikon.</p>\n' +
  '\n' +
  '<p>Jos kohdistus on käyttöliittymän tietyn osion ylälaidassa, <strong>Esc</strong>-näppäimen painaminen\n' +
  '  poistuu myös näppäimistönavigoinnista kokonaan.</p>\n' +
  '\n' +
  '<h1>Suorita valikkokohde tai työkalupalkin painike</h1>\n' +
  '\n' +
  '<p>Kun haluamasi valikkokohde tai työkalupalkin painike on korostettuna, paina <strong>Return</strong>-, <strong>Enter</strong>-\n' +
  '  tai <strong>välilyöntinäppäintä</strong> suorittaaksesi kohteen.</p>\n' +
  '\n' +
  '<h1>Välilehdittömissä valintaikkunoissa navigointi</h1>\n' +
  '\n' +
  '<p>Kun välilehdetön valintaikkuna avautuu, kohdistus siirtyy sen ensimmäiseen interaktiiviseen komponenttiin.</p>\n' +
  '\n' +
  '<p>Voit siirtyä valintaikkunan interaktiivisten komponenttien välillä painamalla <strong>sarkainnäppäintä</strong> tai <strong>Shift+sarkainnäppäin</strong>.</p>\n' +
  '\n' +
  '<h1>Välilehdellisissä valintaikkunoissa navigointi</h1>\n' +
  '\n' +
  '<p>Kun välilehdellinen valintaikkuna avautuu, kohdistus siirtyy välilehtivalikon ensimmäiseen painikkeeseen.</p>\n' +
  '\n' +
  '<p>Voit siirtyä valintaikkunan välilehden interaktiivisen komponenttien välillä painamalla <strong>sarkainnäppäintä</strong> tai\n' +
  '  <strong>Shift+sarkainnäppäin</strong>.</p>\n' +
  '\n' +
  '<p>Voit siirtyä valintaikkunan toiseen välilehteen siirtämällä kohdistuksen välilehtivalikkoon ja painamalla sopivaa <strong>nuolinäppäintä</strong>\n' +
  '  siirtyäksesi käytettävissä olevien välilehtien välillä syklissä.</p>\n');