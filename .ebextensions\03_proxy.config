files:
    "/etc/nginx/conf.d/elasticbeanstalk/proxy.conf" :
        mode: "000644"
        owner: root
        group: root
        content: |
           client_max_body_size 2000M;

           # Additional nginx configuration for static files
           # This supplements the main static file configuration
           location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
               expires 1y;
               add_header Cache-Control "public, immutable";
               add_header Vary Accept-Encoding;

               # Enable gzip compression
               gzip on;
               gzip_vary on;
               gzip_types text/css application/javascript image/svg+xml;
           }

