2025-07-05 05:10:42,608 [ERROR] Command 02_migrate_schemas (source /var/app/venv/*/bin/activate && python manage.py migrate_schemas) failed
2025-07-05 05:10:42,608 [ERROR] Error encountered during build of postbuild_1_mbvs_server_v2: Command 02_migrate_schemas failed
Traceback (most recent call last):
  File "/usr/lib/python3.9/site-packages/cfnbootstrap/construction.py", line 579, in run_config
    CloudFormationCarpenter(config, self._auth_config, self.strict_mode).build(worklog)
  File "/usr/lib/python3.9/site-packages/cfnbootstrap/construction.py", line 277, in build
    changes['commands'] = CommandTool().apply(
  File "/usr/lib/python3.9/site-packages/cfnbootstrap/command_tool.py", line 127, in apply
    raise ToolError(u"Command %s failed" % name)
cfnbootstrap.construction_errors.ToolError: Command 02_migrate_schemas failed
2025-07-05 05:10:42,609 [ERROR] -----------------------BUILD FAILED!------------------------
2025-07-05 05:10:42,609 [ERROR] Unhandled exception during build: Command 02_migrate_schemas failed
Traceback (most recent call last):
  File "/opt/aws/bin/cfn-init", line 181, in <module>
    worklog.build(metadata, configSets, strict_mode)
  File "/usr/lib/python3.9/site-packages/cfnbootstrap/construction.py", line 137, in build
    Contractor(metadata, strict_mode).build(configSets, self)
  File "/usr/lib/python3.9/site-packages/cfnbootstrap/construction.py", line 567, in build
    self.run_config(config, worklog)
  File "/usr/lib/python3.9/site-packages/cfnbootstrap/construction.py", line 579, in run_config
    CloudFormationCarpenter(config, self._auth_config, self.strict_mode).build(worklog)
  File "/usr/lib/python3.9/site-packages/cfnbootstrap/construction.py", line 277, in build
    changes['commands'] = CommandTool().apply(
  File "/usr/lib/python3.9/site-packages/cfnbootstrap/command_tool.py", line 127, in apply
    raise ToolError(u"Command %s failed" % name)
cfnbootstrap.construction_errors.ToolError: Command 02_migrate_schemas failed