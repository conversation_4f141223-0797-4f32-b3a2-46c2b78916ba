#!/bin/bash

# Platform Version Validation Script
# Validates that the EB environment uses the expected platform version
# before deployment to prevent platform drift

set -e

# Configuration
EXPECTED_PLATFORM="Python 3.12 running on 64bit Amazon Linux 2023"
EXPECTED_VERSION="4.6.0"
REGION="${AWS_DEFAULT_REGION:-us-east-1}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Elastic Beanstalk Platform Version Validation ===${NC}"

# Check if environment name is provided
if [ -z "$1" ]; then
    echo -e "${RED}Error: Environment name required${NC}"
    echo "Usage: $0 <environment-name>"
    echo "Example: $0 mbvs-server-v2-staging-updated"
    exit 1
fi

ENVIRONMENT_NAME="$1"

echo "Environment: $ENVIRONMENT_NAME"
echo "Expected Platform: $EXPECTED_PLATFORM v$EXPECTED_VERSION"
echo "Region: $REGION"
echo ""

# Check if AWS CLI is available
if ! command -v aws &> /dev/null; then
    echo -e "${RED}Error: AWS CLI not found${NC}"
    echo "Please install AWS CLI to validate platform version"
    exit 1
fi

# Get environment information
echo "Fetching environment information..."
ENV_INFO=$(aws elasticbeanstalk describe-environments \
    --environment-names "$ENVIRONMENT_NAME" \
    --region "$REGION" \
    --output json 2>/dev/null)

if [ $? -ne 0 ] || [ "$ENV_INFO" = "null" ] || [ -z "$ENV_INFO" ]; then
    echo -e "${RED}Error: Could not fetch environment information${NC}"
    echo "Please check:"
    echo "- Environment name: $ENVIRONMENT_NAME"
    echo "- AWS credentials are configured"
    echo "- Region is correct: $REGION"
    exit 1
fi

# Extract platform information
PLATFORM_ARN=$(echo "$ENV_INFO" | jq -r '.Environments[0].PlatformArn // empty')
SOLUTION_STACK=$(echo "$ENV_INFO" | jq -r '.Environments[0].SolutionStackName // empty')

if [ -z "$PLATFORM_ARN" ] && [ -z "$SOLUTION_STACK" ]; then
    echo -e "${RED}Error: Could not determine platform information${NC}"
    exit 1
fi

# Validate platform
echo "Current Platform Information:"
if [ -n "$PLATFORM_ARN" ]; then
    echo "Platform ARN: $PLATFORM_ARN"
    # Extract version from ARN (format: arn:aws:elasticbeanstalk:region::platform/platform-name/version)
    CURRENT_VERSION=$(echo "$PLATFORM_ARN" | grep -o 'v[0-9]\+\.[0-9]\+\.[0-9]\+' | sed 's/v//')
    
    if [[ "$PLATFORM_ARN" == *"$EXPECTED_PLATFORM"* ]]; then
        if [[ "$PLATFORM_ARN" == *"v$EXPECTED_VERSION"* ]]; then
            echo -e "${GREEN}✅ Platform validation passed${NC}"
            echo "Platform: $EXPECTED_PLATFORM v$EXPECTED_VERSION"
            exit 0
        else
            echo -e "${YELLOW}⚠️  Platform version mismatch${NC}"
            echo "Expected: v$EXPECTED_VERSION"
            echo "Current: v$CURRENT_VERSION"
        fi
    else
        echo -e "${RED}❌ Platform type mismatch${NC}"
        echo "Expected: $EXPECTED_PLATFORM"
        echo "Current: $PLATFORM_ARN"
    fi
elif [ -n "$SOLUTION_STACK" ]; then
    echo "Solution Stack: $SOLUTION_STACK"
    
    if [[ "$SOLUTION_STACK" == *"$EXPECTED_PLATFORM"* ]]; then
        if [[ "$SOLUTION_STACK" == *"v$EXPECTED_VERSION"* ]]; then
            echo -e "${GREEN}✅ Platform validation passed${NC}"
            echo "Platform: $SOLUTION_STACK"
            exit 0
        else
            echo -e "${YELLOW}⚠️  Platform version mismatch${NC}"
            echo "Expected: $EXPECTED_PLATFORM v$EXPECTED_VERSION"
            echo "Current: $SOLUTION_STACK"
        fi
    else
        echo -e "${RED}❌ Platform type mismatch${NC}"
        echo "Expected: $EXPECTED_PLATFORM"
        echo "Current: $SOLUTION_STACK"
    fi
fi

echo ""
echo -e "${YELLOW}Platform validation failed!${NC}"
echo ""
echo "To fix this issue:"
echo "1. Update your EB environment to use the correct platform:"
echo "   eb platform set '$EXPECTED_PLATFORM v$EXPECTED_VERSION'"
echo ""
echo "2. Or update the environment via AWS CLI:"
echo "   aws elasticbeanstalk update-environment \\"
echo "     --environment-name '$ENVIRONMENT_NAME' \\"
echo "     --solution-stack-name '64bit Amazon Linux 2023 v$EXPECTED_VERSION running Python 3.12'"
echo ""
echo "3. Or use the AWS Console to update the platform version"

exit 1
