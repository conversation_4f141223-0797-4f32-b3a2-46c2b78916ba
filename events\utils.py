import requests
from rest_framework.response import Response
from rest_framework import status

#subdomain_prefix = hostname.split('.')[0]
def tenant_from_request(request):
    hostname = request.get_host()
    return hostname.split('.')[0]

# def tenant_from_frontend_subdomain(request):
#     try:
#         frontend_sub_domain = request.data['subdomain']
#         return frontend_sub_domain
#     except KeyError:
#         res = {'error': 'please provide the subdomain field'}
#         return Response(res, status=status.HTTP_400_BAD_REQUEST)

def export_boolean(bool_value):
    return str(bool_value).upper()


def show_list(list):
    values = [value.name for value in list]
    return " \\ ".join(values)
