.top-button {
  display: flex;
  justify-content: space-between;
}

.back-btn, .media-btn, .submit-btn {
  padding: 5px 20px;
  color: #fff;
  background: #00253A;
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: capitalize;
  border: 2px #25bfff solid;
  border-radius: 20px;
  cursor: pointer;
  text-decoration: none;
}
  
.header-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.subheader {
  width: 80%;
}

form {
  margin-left: 2rem;
  margin-right: 2rem;
}

.custom-select {
  width: 35%;
  margin-top: 0.4rem;
  margin-bottom: 1.5rem;
}

.input-group {
  width: 35%;
  margin-top: 0.4rem;
  margin-bottom: 2rem;
}

.submit-btn {
  margin-top: 1.5rem; 
}

.return-info {
  margin: 1rem 3rem 0 3rem;
}

.back-btn:hover, .media-btn:hover, .submit-btn:hover {
  color: rgba(255, 255, 255, 0.5)
}