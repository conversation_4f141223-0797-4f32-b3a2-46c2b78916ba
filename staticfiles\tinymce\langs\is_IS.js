tinymce.addI18n("is_IS",{"Redo":"Endurkalla","Undo":"Afturkalla","Cut":"<PERSON><PERSON><PERSON>","Copy":"Afrita","Paste":"<PERSON>\xedma","Select all":"Velja allt","New document":"N\xfdtt skjal","Ok":"Sta\xf0festa","Cancel":"H\xe6tta vi\xf0","Visual aids":"Sj\xf3nr\xe6n hj\xe1lp","Bold":"Feitletra\xf0","Italic":"Skr\xe1letra\xf0","Underline":"Undirstrika\xf0","Strikethrough":"Yfirstrika\xf0","Superscript":"Uppskrift","Subscript":"Ni\xf0urskrifa\xf0","Clear formatting":"Hreinsa sni\xf0","Remove":"","Align left":"Vinst<PERSON>ja<PERSON><PERSON>","Align center":"Mi\xf0jujafna","Align right":"H\xe6grijafna","No alignment":"","Justify":"Jafna","Bullet list":"K\xfalu listi","Numbered list":"N\xfamera\xf0ur listi","Decrease indent":"Minnka inndr\xe1tt","Increase indent":"Auka inndr\xe1tt","Close":"Loka","Formats":"Sni\xf0m\xe1t","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"Vafrinn \xfeinn sty\xf0ur ekki beinann a\xf0gang a\xf0 klippibor\xf0inu.  Nota\xf0u Ctrl-X/C/V \xe1 lyklabor\xf0inu \xed sta\xf0inn.","Headings":"Fyrirsagnir","Heading 1":"Fyrirs\xf6gn 1","Heading 2":"Fyrirs\xf6gn 2","Heading 3":"Fyrirs\xf6gn 3","Heading 4":"Fyrirs\xf6gn 4","Heading 5":"Fyrirs\xf6gn 5","Heading 6":"Fyrirs\xf6gn 6","Preformatted":"","Div":"","Pre":"\xd3st\xedla\xf0","Code":"K\xf3\xf0i","Paragraph":"M\xe1lsgrein","Blockquote":"Blokk","Inline":"Inndregi\xf0","Blocks":"Blokkir","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"L\xedming er \xed textaham.  Texti ver\xf0ur l\xedmdur sem l\xe1tlaus \xfeanga\xf0 til \xfe\xfa afhakar vi\xf0 \xfeennan valm\xf6guleika.","Fonts":"Leturger\xf0ir","Font sizes":"","Class":"Tegund","Browse for an image":"Finna mynd \xed t\xf6lvunni","OR":"E\xd0A","Drop an image here":"Settu inn mynd h\xe9r","Upload":"Hla\xf0a upp","Uploading image":"","Block":"Blokka","Align":"Stilla","Default":"Sj\xe1lfgefi\xf0","Circle":"Hringur","Disc":"Diskur","Square":"Ferningur","Lower Alpha":"L\xe1gstafir Alpha","Lower Greek":"L\xe1gstafir Gr\xedskir","Lower Roman":"L\xe1gstafir R\xf3mverskir","Upper Alpha":"H\xe1stafir Alpha","Upper Roman":"H\xe1stafir R\xf3mverskir","Anchor...":"","Anchor":"","Name":"Nafn","ID":"","ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.":"","You have unsaved changes are you sure you want to navigate away?":"\xdea\xf0 eru \xf3vista\xf0ar breytingar, ertu viss um a\xf0 \xfe\xfa viljir vafra \xed burtu?","Restore last draft":"Endurkalla s\xed\xf0asta uppkast","Special character...":"S\xe9rstakur stafur...","Special Character":"","Source code":"Frumk\xf3\xf0i","Insert/Edit code sample":"Setja inn/breyta k\xf3\xf0ad\xe6mi","Language":"Tungum\xe1l","Code sample...":"K\xf3\xf0ad\xe6mi...","Left to right":"Vinstri til h\xe6gri","Right to left":"H\xe6gri til vinstri","Title":"Titill","Fullscreen":"Fylla skj\xe1","Action":"A\xf0ger\xf0","Shortcut":"Fl\xfdtilei\xf0","Help":"Hj\xe1lp","Address":"","Focus to menubar":"F\xf3kus \xe1 menubar","Focus to toolbar":"F\xf3kus \xe1 t\xe6kjastiku","Focus to element path":"F\xf3kus \xe1 element path","Focus to contextual toolbar":"F\xf3kus \xe1 contextual toolbar","Insert link (if link plugin activated)":"Setja inn hlekk (ef link vi\xf0b\xf3t er virk)","Save (if save plugin activated)":"Vista (ef vistunar vi\xf0b\xf3t er virk)","Find (if searchreplace plugin activated)":"Leita (ef searchreplace vi\xf0b\xf3t er virk)","Plugins installed ({0}):":"Uppsettar vi\xf0b\xf3tir ({0}):","Premium plugins:":"Premium vi\xf0b\xe6tur:","Learn more...":"Sj\xe1 meira...","You are using {0}":"\xde\xfa ert a\xf0 nota {0}","Plugins":"Vi\xf0b\xe6tur","Handy Shortcuts":"Sni\xf0ugar fl\xfdtilei\xf0ir","Horizontal line":"L\xe1r\xe9tt l\xedna","Insert/edit image":"Setja inn/breyta mynd","Alternative description":"","Accessibility":"","Image is decorative":"","Source":"Sl\xf3\xf0i","Dimensions":"Hlutf\xf6ll","Constrain proportions":"Halda hlutf\xf6llum","General":"Almennt","Advanced":"\xcdtarlegt","Style":"St\xedll","Vertical space":"L\xf3\xf0r\xe9tt bil","Horizontal space":"L\xe1r\xe9tt bil","Border":"Rammi","Insert image":"Setja inn mynd","Image...":"Mynd...","Image list":"Myndalisti","Resize":"Breyta st\xe6r\xf0","Insert date/time":"Setja inn dagsetningu/t\xedma","Date/time":"Dagur/t\xedmi","Insert/edit link":"Setja inn/breyta hlekk","Text to display":"Texti til a\xf0 s\xfdna","Url":"Veffang","Open link in...":"Opna hlekk \xed...","Current window":"N\xfaverandi gluggi","None":"Ekkert","New window":"N\xfdr gluggi","Open link":"","Remove link":"Fjarl\xe6gja hlekk","Anchors":"Akkeri","Link...":"Hlekkur...","Paste or type a link":"L\xedma e\xf0a sl\xe1 inn hlekk","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":"Hlekkurinn sem \xfe\xfa rita\xf0ir inn l\xfdtur \xfat fyrir a\xf0 vera netfang. Viltu b\xe6ta vi\xf0 forskeytinu mailto: ?","The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":"Hlekkurinn sem \xfe\xfa rita\xf0ir inn l\xfdtur \xfat fyrir a\xf0 vera ytri hlekkur. Viltu b\xe6ta vi\xf0 forskeytinu http:// ?","The URL you entered seems to be an external link. Do you want to add the required https:// prefix?":"","Link list":"Listi hlekkja","Insert video":"Setja inn myndband","Insert/edit video":"Setja inn/fjarl\xe6gja myndband","Insert/edit media":"Setja inn/breyta myndefni","Alternative source":"Valkv\xe6\xf0ur frumk\xf3\xf0i","Alternative source URL":"","Media poster (Image URL)":"Media poster (mynd URL)","Paste your embed code below:":"L\xedma frumk\xf3\xf0a fyrir ne\xf0an:","Embed":"Hengja vi\xf0","Media...":"","Nonbreaking space":"Bil sem brotnar ekki","Page break":"S\xed\xf0ubrot","Paste as text":"L\xedma sem texta","Preview":"Forsko\xf0un","Print":"","Print...":"Prenta...","Save":"Vista","Find":"Finna","Replace with":"Skipta \xfat me\xf0","Replace":"Skipta \xfat","Replace all":"Skipta \xf6llum \xfat","Previous":"Fyrri","Next":"N\xe6sti","Find and Replace":"","Find and replace...":"","Could not find the specified string.":"Fann ekki umbe\xf0inn streng.","Match case":"Samanbur\xf0ur","Find whole words only":"Finna einungis heil or\xf0","Find in selection":"","Insert table":"Setja inn t\xf6flu","Table properties":"Stillingar t\xf6flu","Delete table":"Ey\xf0a t\xf6flu","Cell":"Reitur","Row":"R\xf6\xf0","Column":"D\xe1lkur","Cell properties":"Stillingar reits","Merge cells":"Sameina reiti","Split cell":"Deila reiti","Insert row before":"Setja inn r\xf6\xf0 fyrir framan","Insert row after":"Setja inn r\xf6\xf0 fyrir aftan","Delete row":"Ey\xf0a r\xf6\xf0","Row properties":"Stillingar ra\xf0ar","Cut row":"Klippa r\xf6\xf0","Cut column":"","Copy row":"Afrita r\xf6\xf0","Copy column":"","Paste row before":"L\xedma r\xf6\xf0 fyrir framan","Paste column before":"","Paste row after":"L\xedma r\xf6\xf0 fyrir aftan","Paste column after":"","Insert column before":"Setja inn d\xe1lk fyrir framan","Insert column after":"Setja inn d\xe1lk fyrir aftan","Delete column":"Ey\xf0a d\xe1lki","Cols":"D\xe1lkar","Rows":"Ra\xf0ir","Width":"Breidd","Height":"H\xe6\xf0","Cell spacing":"Bil \xed reit","Cell padding":"R\xfdmi reits","Row clipboard actions":"","Column clipboard actions":"","Table styles":"","Cell styles":"","Column header":"","Row header":"","Table caption":"","Caption":"Titill","Show caption":"S\xfdna myndatexta","Left":"Vinstri","Center":"Mi\xf0ja","Right":"H\xe6gri","Cell type":"Tegund reits","Scope":"Gildissvi\xf0","Alignment":"J\xf6fnun","Horizontal align":"","Vertical align":"","Top":"Efst","Middle":"Mi\xf0ja","Bottom":"Ne\xf0st","Header cell":"Reitarhaus","Row group":"H\xf3pur ra\xf0ar","Column group":"H\xf3pur d\xe1lks","Row type":"Tegund ra\xf0ar","Header":"Fyrirs\xf6gn","Body":"Innihald","Footer":"Ne\xf0anm\xe1l","Border color":"Litur \xe1 ramma","Solid":"","Dotted":"","Dashed":"","Double":"","Groove":"","Ridge":"","Inset":"","Outset":"","Hidden":"","Insert template...":"Setja inn sni\xf0m\xe1t...","Templates":"Sni\xf0m\xe1t","Template":"Sni\xf0m\xe1t","Insert Template":"","Text color":"Litur texta","Background color":"Bakgrunnslitur","Custom...":"S\xe9rsni\xf0i\xf0...","Custom color":"S\xe9rsni\xf0in litur","No color":"Enginn litur","Remove color":"Fjarl\xe6gja lit","Show blocks":"S\xfdna kubba","Show invisible characters":"S\xfdna \xf3s\xfdnilega stafi","Word count":"Or\xf0afj\xf6ldi","Count":"Fj\xf6ldi","Document":"Skjal","Selection":"Val","Words":"Or\xf0","Words: {0}":"Or\xf0:  {0}","{0} words":"{0} or\xf0","File":"Skjal","Edit":"Breyta","Insert":"Setja inn","View":"Sko\xf0a","Format":"Sni\xf0","Table":"Tafla","Tools":"T\xf3l","Powered by {0}":"Keyrt af {0}","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"Textasv\xe6\xf0i \xed \xedtarham.  \xddttu \xe1 ALT-F9 fyrir valmynd.  \xddttu \xe1 ALT-F10 fyrir t\xf3lastiku.  \xddttu \xe1 ALT-0 fyrir a\xf0sto\xf0.","Image title":"Titill myndar","Border width":"","Border style":"","Error":"Villa","Warn":"A\xf0vara","Valid":"Gilt","To open the popup, press Shift+Enter":"\xddttu \xe1 Shift+Enter til a\xf0 opna sprettiglugga","Rich Text Area":"","Rich Text Area. Press ALT-0 for help.":"Rich Text sv\xe6\xf0i. Smelltu \xe1 Alt-0 fyrir hj\xe1lp","System Font":"Kerfis leturger\xf0","Failed to upload image: {0}":"Gat ekki hala\xf0 upp mynd: {0}","Failed to load plugin: {0} from url {1}":"Gat ekki hla\xf0i\xf0 vi\xf0b\xf3t: {0} fr\xe1 urli {1}","Failed to load plugin url: {0}":"Gat ekki hla\xf0i\xf0 vi\xf0b\xf3t url: {0}","Failed to initialize plugin: {0}":"Val","example":"d\xe6mi","Search":"Leita","All":"Allt","Currency":"Gjaldmi\xf0ill","Text":"Texti","Quotations":"Tilvitnanir","Mathematical":"St\xe6r\xf0fr\xe6\xf0i","Extended Latin":"","Symbols":"T\xe1kn","Arrows":"\xd6rvar","User Defined":"Stillt af notanda","dollar sign":"dollar t\xe1kn","currency sign":"gjaldmi\xf0ils t\xe1kn","euro-currency sign":"evru-gjaldmi\xf0ils t\xe1kn","colon sign":"kommu t\xe1kn","cruzeiro sign":"cruzeiro t\xe1kn","french franc sign":"franskur franki t\xe1kn","lira sign":"lira t\xe1kn","mill sign":"mill t\xe1kn","naira sign":"naira t\xe1kn","peseta sign":"peseta t\xe1kn","rupee sign":"rupee t\xe1kn","won sign":"won t\xe1kn","new sheqel sign":"new sheqel t\xe1kn","dong sign":"dong t\xe1kn","kip sign":"kip t\xe1kn","tugrik sign":"tugrik t\xe1kn","drachma sign":"drachma t\xe1kn","german penny symbol":"german penny t\xe1kn","peso sign":"peso t\xe1kn","guarani sign":"guarani t\xe1kn","austral sign":"austral t\xe1kn","hryvnia sign":"hryvnia t\xe1kn","cedi sign":"cedi t\xe1kn","livre tournois sign":"livre tournois t\xe1kn","spesmilo sign":"spesmilo t\xe1kn","tenge sign":"tenge t\xe1kn","indian rupee sign":"indverskt rupee t\xe1kn","turkish lira sign":"tyrknesk l\xedra t\xe1kn","nordic mark sign":"nordic mark t\xe1kn","manat sign":"manat t\xe1kn","ruble sign":"ruble t\xe1kn","yen character":"yen stafur","yuan character":"yuan stafur","yuan character, in hong kong and taiwan":"yuan stafur, \xed Hong Kong og Ta\xedvan","yen/yuan character variant one":"yen/yuan t\xe1kn afbrig\xf0i eitt","Emojis":"","Emojis...":"","Loading emojis...":"","Could not load emojis":"","People":"F\xf3lk","Animals and Nature":"D\xfdr og n\xe1tt\xfara","Food and Drink":"Matur og drykkur","Activity":"Virkni","Travel and Places":"Fer\xf0al\xf6g og sta\xf0ir","Objects":"Hlutir","Flags":"F\xe1nar","Characters":"Stafabil","Characters (no spaces)":"Stafabil (engin bil)","{0} characters":"{0} stafabil","Error: Form submit field collision.":"Villa: Form submit field collision.","Error: No form element found.":"Villa: Ekkert form element fannst.","Color swatch":"Litaval","Color Picker":"Litaval","Invalid hex color code: {0}":"","Invalid input":"","R":"","Red component":"","G":"","Green component":"","B":"","Blue component":"","#":"","Hex color code":"","Range 0 to 255":"","Turquoise":"Gr\xe6nbl\xe1r","Green":"Gr\xe6nn","Blue":"Bl\xe1r","Purple":"Fj\xf3lubl\xe1r","Navy Blue":"D\xf6kkbl\xe1r","Dark Turquoise":"D\xf6kk gr\xe6nbl\xe1r","Dark Green":"D\xf6kkgr\xe6nn","Medium Blue":"Mi\xf0lungs bl\xe1r","Medium Purple":"Mi\xf0lungs fj\xf3lubl\xe1r","Midnight Blue":"N\xe6turbl\xe1r","Yellow":"Gulur","Orange":"Appels\xednugulur","Red":"Rau\xf0ur","Light Gray":"Lj\xf3sgr\xe1r","Gray":"Gr\xe1r","Dark Yellow":"D\xf6kkgulur","Dark Orange":"D\xf6kk appels\xednugulur","Dark Red":"D\xf6kkrau\xf0ur","Medium Gray":"Mi\xf0lungs gr\xe1r","Dark Gray":"D\xf6kkgr\xe1r","Light Green":"Lj\xf3sgr\xe6nn","Light Yellow":"Lj\xf3sgulur","Light Red":"Lj\xf3srau\xf0ur","Light Purple":"Lj\xf3s fj\xf3lubl\xe1r","Light Blue":"Lj\xf3sbl\xe1r","Dark Purple":"D\xf6kk fj\xf3lubl\xe1r","Dark Blue":"D\xf6kkbl\xe1r","Black":"Svartur","White":"Hv\xedtur","Switch to or from fullscreen mode":"Skipta \xed e\xf0a \xfar fullum skj\xe1","Open help dialog":"Opna hj\xe1lparkassa","history":"saga","styles":"st\xedlar","formatting":"","alignment":"textastilling","indentation":"inndr\xe1ttur","Font":"Leturger\xf0","Size":"St\xe6r\xf0","More...":"Meira...","Select...":"Velja...","Preferences":"Stillingar","Yes":"J\xe1","No":"Nei","Keyboard Navigation":"Lyklabor\xf0 Navigation","Version":"\xdatg\xe1fa","Code view":"","Open popup menu for split buttons":"","List Properties":"","List properties...":"","Start list at number":"","Line height":"","Dropped file type is not supported":"","Loading...":"","ImageProxy HTTP error: Rejected request":"","ImageProxy HTTP error: Could not find Image Proxy":"","ImageProxy HTTP error: Incorrect Image Proxy URL":"","ImageProxy HTTP error: Unknown ImageProxy error":""});