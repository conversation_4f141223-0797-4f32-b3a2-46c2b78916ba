from django.db import connection
from django.utils import timezone
import random



def get_current_tenant():
    """ Return current tenant
    Determine based on connection schema_name """
    return connection.schema_name

def export_boolean(bool_value):
    return str(bool_value).upper()

def export_date_time(datetime):
    if datetime:
        local_time = datetime.replace(tzinfo=timezone.utc).astimezone(tz=None)
        return local_time.strftime("%Y-%m-%d %H:%M:%S")
    return datetime

def show_list(list):
    values = [value.name for value in list]
    return " \\ ".join(values)

def show_users(list):
    values = [value.user.email for value in list]
    return " \\ ".join(values)
    

def resource_files_path(instance, filename):
    return f'resource_files/MB{random.randint(1000, 99999)}-{filename}'


def inspect_html_blurb(html_blurb):
    if html_blurb:
        if '&nbsp;' in html_blurb:
            html_blurb = html_blurb.replace('&nbsp;', ' ')
        if '&amp;' in html_blurb:
            html_blurb = html_blurb.replace('&amp;', '&')
    return html_blurb

def join_with_backslash(values):
    return ' \\ '.join(values)
