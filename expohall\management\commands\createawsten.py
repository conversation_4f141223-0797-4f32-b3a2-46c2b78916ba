from django.core.management.base import BaseCommand
from events.models import Event, Domain
import os


class Command(BaseCommand):

    def handle(self, *args, **options):
        if 'ENVIRONMENT' in os.environ:
            if os.environ['ENVIRONMENT'] == 'development':
                url = 'mbvs-server-development.com'
                dev_url = 'develop.mbvs-server-development.com'
            elif os.environ['ENVIRONMENT'] == 'staging':
                url = 'mbvs-server-staging.com'
                dev_url = 'develop.mbvs-server-staging.com'
            elif os.environ['ENVIRONMENT'] == 'production':
                url = 'mbvs-server.com'
                dev_url = 'develop.mbvs-server.com'
        else:
            url = 'mbvs-server.com'
            dev_url = 'develop.mbvs-server.com'        
        
        if not Event.objects.filter(name="public").exists():
            event = Event.objects.create(domain_url=url, schema_name='public', name='public')
            domain = Domain()
            domain.domain = event.domain_url
            domain.tenant = event
            domain.is_primary = True
            domain.save()
        if not Event.objects.filter(name="develop").exists():
            event = Event.objects.create(domain_url=dev_url, schema_name='develop', name='develop')
            domain = Domain()
            domain.domain = event.domain_url
            domain.tenant = event
            domain.is_primary = True
            domain.save()

        else:
            e = Event.objects.get(name="public")
            d = Domain.objects.get(tenant=e)
            d.domain = e.domain_url
            d.save()
            e2 = Event.objects.get(name="develop")
            d2 = Domain.objects.get(tenant=e2)
            d2.domain = e2.domain_url
            d2.save()
