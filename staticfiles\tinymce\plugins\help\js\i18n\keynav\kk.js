tinymce.Resource.add('tinymce.html-i18n.help-keynav.kk',
'<h1>Пернетақта навигациясын бастау</h1>\n' +
  '\n' +
  '<dl>\n' +
  '  <dt>Мәзір жолағын фокустау</dt>\n' +
  '  <dd>Windows немесе Linux: Alt+F9</dd>\n' +
  '  <dd>macOS: &#x2325;F9</dd>\n' +
  '  <dt>Құралдар тақтасын фокустау</dt>\n' +
  '  <dd>Windows немесе Linux: Alt+F10</dd>\n' +
  '  <dd>macOS: &#x2325;F10</dd>\n' +
  '  <dt>Төменгі деректемені фокустау</dt>\n' +
  '  <dd>Windows немесе Linux: Alt+F11</dd>\n' +
  '  <dd>macOS: &#x2325;F11</dd>\n' +
  '  <dt>Мәтінмәндік құралдар тақтасын фокустау</dt>\n' +
  '  <dd>Windows, Linux немесе macOS: Ctrl+F9\n' +
  '</dl>\n' +
  '\n' +
  '<p>Навигация бөлектелетін немесе Төменгі деректеме элементінің жолындағы бірінші элемент жағдайында асты сызылатын\n' +
  '  бірінші ПИ элементінен басталады.</p>\n' +
  '\n' +
  '<h1>ПИ бөлімдері арасында навигациялау</h1>\n' +
  '\n' +
  '<p>Бір ПИ бөлімінен келесісіне өту үшін <strong>Tab</strong> пернесін басыңыз.</p>\n' +
  '\n' +
  '<p>Бір ПИ бөлімінен алдыңғысына өту үшін <strong>Shift+Tab</strong> пернесін басыңыз.</p>\n' +
  '\n' +
  '<p>Осы ПИ бөлімдерінің <strong>Tab</strong> реті:</p>\n' +
  '\n' +
  '<ol>\n' +
  '  <li>Мәзір жолағы</li>\n' +
  '  <li>Әрбір құралдар тақтасы тобы</li>\n' +
  '  <li>Бүйірлік жолақ</li>\n' +
  '  <li>Төменгі деректемедегі элемент жолы</li>\n' +
  '  <li>Төменгі деректемедегі сөздер санын ауыстыру түймесі</li>\n' +
  '  <li>Төменгі деректемедегі брендингтік сілтеме</li>\n' +
  '  <li>Төменгі деректемедегі редактор өлшемін өзгерту тұтқасы</li>\n' +
  '</ol>\n' +
  '\n' +
  '<p>ПИ бөлімі көрсетілмесе, ол өткізіп жіберіледі.</p>\n' +
  '\n' +
  '<p>Төменгі деректемеде пернетақта навигациясының фокусы болса және бүйірлік жолақ көрінбесе, <strong>Shift+Tab</strong> тіркесімін басу әрекеті\n' +
  '  фокусты соңғысы емес, бірінші құралдар тақтасы тобына жылжытады.</p>\n' +
  '\n' +
  '<h1>ПИ бөлімдерінде навигациялау</h1>\n' +
  '\n' +
  '<p>Бір ПИ элементінен келесісіне өту үшін <strong>Arrow</strong> (Көрсеткі) пернесін басыңыз.</p>\n' +
  '\n' +
  '<p><strong>Left</strong> (Сол жақ) және <strong>Right</strong> (Оң жақ) көрсеткі пернелері</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>мәзір жолағындағы мәзірлер арасында жылжыту.</li>\n' +
  '  <li>мәзірде ішкі мәзірді ашу.</li>\n' +
  '  <li>құралдар тақтасы тобындағы түймелер арасында жылжыту.</li>\n' +
  '  <li>төменгі деректеме элементінің жолындағы элементтер арасында жылжыту.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p><strong>Down</strong> (Төмен) және <strong>Up</strong> (Жоғары) көрсеткі пернелері</p>\n' +
  '\n' +
  '<ul>\n' +
  '  <li>мәзірдегі мәзір элементтері арасында жылжыту.</li>\n' +
  '  <li>құралдар тақтасының ашылмалы мәзіріндегі мәзір элементтері арасында жылжыту.</li>\n' +
  '</ul>\n' +
  '\n' +
  '<p>Фокусталған ПИ бөліміндегі <strong>Arrow</strong> (Көрсеткі) пернелерінің циклі.</p>\n' +
  '\n' +
  '<p>Ашық мәзірді жабу үшін ішкі мәзірді ашып немесе ашылмалы мәзірді ашып, <strong>Esc</strong> пернесін басыңыз.</p>\n' +
  '\n' +
  '<p>Ағымдағы фокус белгілі бір ПИ бөлімінің «үстінде» болса, <strong>Esc</strong> пернесін басу әрекеті пернетақта\n' +
  '  навигациясын толығымен жабады.</p>\n' +
  '\n' +
  '<h1>Мәзір элементін немесе құралдар тақтасы түймесін орындау</h1>\n' +
  '\n' +
  '<p>Қажетті мәзір элементі немесе құралдар тақтасы түймесі бөлектелген кезде, элементті орындау үшін <strong>Return</strong> (Қайтару), <strong>Enter</strong> (Енгізу)\n' +
  '  немесе <strong>Space bar</strong> (Бос орын) пернесін басыңыз.</p>\n' +
  '\n' +
  '<h1>Белгіленбеген диалог терезелерін навигациялау</h1>\n' +
  '\n' +
  '<p>Белгіленбеген диалог терезелерінде диалог терезесі ашылған кезде бірінші интерактивті құрамдас фокусталады.</p>\n' +
  '\n' +
  '<p><strong>Tab</strong> немесе <strong>Shift+Tab</strong> пернесін басу арқылы интерактивті диалог терезесінің құрамдастары арасында навигациялаңыз.</p>\n' +
  '\n' +
  '<h1>Белгіленген диалог терезелерін навигациялау</h1>\n' +
  '\n' +
  '<p>Белгіленген диалог терезелерінде диалог терезесі ашылған кезде қойынды мәзіріндегі бірінші түйме фокусталады.</p>\n' +
  '\n' +
  '<p><strong>Tab</strong> немесе\n' +
  '  <strong>Shift+Tab</strong> пернесін басу арқылы осы диалог терезесі қойындысының интерактивті құрамдастары арасында навигациялаңыз.</p>\n' +
  '\n' +
  '<p>Қойынды мәзірінің фокусын беру арқылы басқа диалог терезесінің қойындысына ауысып, тиісті <strong>Arrow</strong> (Көрсеткі)\n' +
  '  пернесін басу арқылы қолжетімді қойындылар арасында айналдыруға болады.</p>\n');