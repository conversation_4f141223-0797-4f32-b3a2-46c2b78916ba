client_max_body_size 2000M;
proxy_connect_timeout 600;
proxy_send_timeout 600;
proxy_read_timeout 600;
send_timeout 600;

# Static files configuration for Django on AL2023
location /static/ {
    alias /var/app/current/staticfiles/;
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Access-Control-Allow-Origin "*";

    # Security headers for static files
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;

    # Gzip compression for static files
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Handle missing files gracefully
    try_files $uri $uri/ =404;
}

# Optional: Media files configuration
location /media/ {
    alias /var/app/current/media/;
    expires 30d;
    add_header Cache-Control "public";
    add_header Access-Control-Allow-Origin "*";
}

server {
    server_tokens off;
}
