
2025/07/05 04:53:18 [error] 24743#24743: *47 open() "/var/app/current/static/backend_css/universal_styles.css" failed (2: No such file or directory), client: ************, server: , request: "GET /static/backend_css/universal_styles.css HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/"
2025/07/05 04:53:18 [error] 24743#24743: *47 open() "/var/app/current/static/backend_css/dashboard_styles.css" failed (2: No such file or directory), client: ************, server: , request: "GET /static/backend_css/dashboard_styles.css HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/"
2025/07/05 04:53:22 [error] 24743#24743: *47 open() "/var/app/current/static/backend_css/universal_styles.css" failed (2: No such file or directory), client: ************, server: , request: "GET /static/backend_css/universal_styles.css HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/"
2025/07/05 04:53:22 [error] 24742#24742: *52 open() "/var/app/current/static/backend_css/dashboard_styles.css" failed (2: No such file or directory), client: ************, server: , request: "GET /static/backend_css/dashboard_styles.css HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/"
2025/07/05 04:53:23 [error] 24743#24743: *47 open() "/var/app/current/static/icon/matchbox.ico" failed (2: No such file or directory), client: ************, server: , request: "GET /static/icon/matchbox.ico?v=2 HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/"
2025/07/05 04:54:25 [error] 24743#24743: *47 open() "/var/app/current/static/icon/matchbox.ico" failed (2: No such file or directory), client: ************, server: , request: "GET /static/icon/matchbox.ico?v=2 HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/login_client/"
2025/07/05 05:28:04 [error] 24744#24744: *619 open() "/var/app/current/static/icon/matchbox.ico" failed (2: No such file or directory), client: *************, server: , request: "GET /static/icon/matchbox.ico?v=2 HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/login_client/"
2025/07/05 05:28:17 [error] 24744#24744: *619 open() "/var/app/current/static/backend_css/universal_styles.css" failed (2: No such file or directory), client: *************, server: , request: "GET /static/backend_css/universal_styles.css HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/"
2025/07/05 05:28:17 [error] 24744#24744: *627 open() "/var/app/current/static/backend_css/dashboard_styles.css" failed (2: No such file or directory), client: *************, server: , request: "GET /static/backend_css/dashboard_styles.css HTTP/1.1", host: "mbvs-server-development.com", referrer: "https://mbvs-server-development.com/"
2025/07/05 05:30:08 [warn] 26898#26898: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/07/05 05:30:08 [warn] 26898#26898: conflicting server name "" on 0.0.0.0:80, ignored
2025/07/05 05:30:08 [warn] 26932#26932: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/07/05 05:30:08 [warn] 26932#26932: conflicting server name "" on 0.0.0.0:80, ignored
2025/07/05 05:30:08 [warn] 26934#26934: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/07/05 05:30:08 [warn] 26934#26934: conflicting server name "" on 0.0.0.0:80, ignored
2025/07/05 05:33:32 [warn] 27497#27497: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/07/05 05:33:32 [warn] 27497#27497: conflicting server name "" on 0.0.0.0:80, ignored
2025/07/05 05:33:32 [warn] 27531#27531: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/07/05 05:33:32 [warn] 27531#27531: conflicting server name "" on 0.0.0.0:80, ignored
2025/07/05 05:33:32 [warn] 27532#27532: could not build optimal types_hash, you should increase either types_hash_max_size: 1024 or types_hash_bucket_size: 64; ignoring types_hash_bucket_size
2025/07/05 05:33:32 [warn] 27532#27532: conflicting server name "" on 0.0.0.0:80, ignored
2025/07/05 06:08:33 [emerg] 30222#30222: "location" directive is not allowed here in /var/proxy/staging/nginx/conf.d/proxy.conf:8