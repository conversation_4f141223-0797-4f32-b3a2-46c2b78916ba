os: linux
dist: focal

language: python
python:  
  - "3.12"

jobs:
  include:
    - name: production branch
      if: branch = production
      # command to install dependencies
      install:
        - pip install -r requirements.txt
      # command to run tests
      script:
        - pytest spaces/tests.py
      deploy:
        provider: elasticbeanstalk
        edge: true
        access_key_id: $AWS_ACCESS_KEY
        secret_access_key: $AWS_SECRET_KEY
        region: "us-east-1"  
        app: "mbvs-server-v2-prod"
        env: "mbvs-server-prod-v2-env"
        bucket: "elasticbeanstalk-us-east-1-376529569492"
        bucket_path: "mbvs-server-prod-v2-env"
        on:
          branch: production
    
    - name: staging branch
      if: branch = main
      # command to install dependencies
      install:
        - pip install -r requirements.txt
      # command to run tests
      script:
        - pytest spaces/tests.py
      deploy:
        provider: elasticbeanstalk
        edge: true
        access_key_id: $AWS_ACCESS_KEY
        secret_access_key: $AWS_SECRET_KEY
        region: "us-east-1"  
        app: "mbvs-server-v2"
        env: "mbvs-server-v2-staging-updated" 
        bucket: "elasticbeanstalk-us-east-1-376529569492"
        bucket_path: "mbvs-server-v2"
        on:
          branch: main

    - name: development branch
      if: branch = dev
      # command to install dependencies
      install:
        - pip install -r requirements.txt
      # command to run tests
      script:
        - pytest spaces/tests.py
      deploy:
        provider: elasticbeanstalk
        edge: true
        access_key_id: $AWS_ACCESS_KEY
        secret_access_key: $AWS_SECRET_KEY
        region: "us-east-1"  
        app: "mbvs-server-v2"
        env: "mbvs-server-v2-dev-updated" 
        bucket: "elasticbeanstalk-us-east-1-376529569492"
        bucket_path: "mbvs-server-v2"
        on:
          branch: dev