# mbvs-server-v2

A virtual convention like exhibition hall, the second version

## Backend startup (No Docker)

1. Install venv in project directory: python3 -m venv ./venv
2. Activate virtual environment: source venv/bin/activate
3. Install requirements: pip3 install -r requirements.txt
4. Make migration to the database: python3 manage.py makemigrations && python3 manage.py migrate_schemas (need postgres for this)
5. create tenants:

```shell
./manage.py shell
>>> from tenants.models import Tenant
>>> t = Tenant(domain_url='localhost',schema_name='public',name='public')
>>> t.save()
>>> t2 = Tenant(domain_url='dev.localhost',schema_name='dev',name='Dev')
>>> t2.save()
```

6. Start backend: python3 manage.py runserver —nostatic
7. View it on a web browser at localhost:8000 or dev.localhost:8000

## Backend startup (With Docker)

1. Install Docker on to your machine (https://www.docker.com/)
2. cd into project directory
3. Build the docker project: `docker-compose up --build`
4. View it on a web browser at localhost:8002 or dev.localhost:8002
