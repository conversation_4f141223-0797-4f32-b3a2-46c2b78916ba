tinymce.addI18n("ga",{"Redo":"Athdh\xe9an","Undo":"<PERSON><PERSON><PERSON>","Cut":"<PERSON>r","Copy":"C\xf3ipe\xe1il","Paste":"<PERSON><PERSON><PERSON><PERSON>","Select all":"<PERSON>oghn<PERSON>gh uile","New document":"C\xe1ip\xe9is nua","Ok":"OK","Cancel":"Cealaigh","Visual aids":"\xc1iseanna amhairc","Bold":"Trom","Italic":"Iod\xe1lach","Underline":"Fol\xedne","Strikethrough":"L\xedne tr\xedd","Superscript":"Forscript","Subscript":"Foscript","Clear formatting":"Glan form\xe1idi\xfa","Remove":"","Align left":"Ail\xednigh ar chl\xe9","Align center":"Ail\xednigh sa l\xe1r","Align right":"Ail\xednigh ar dheis","No alignment":"","Justify":"Comhfhadaigh","Bullet list":"Liosta Urchar","Numbered list":"Liosta Uimhrithe","Decrease indent":"Laghdaigh eang","Increase indent":"M\xe9adaigh eang","Close":"D\xfan","Formats":"Form\xe1id\xed","Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X/C/V keyboard shortcuts instead.":"N\xed f\xe9idir le do bhrabhs\xe1la\xed teacht go d\xedreach ar an ngearrthaisce. Bain \xfas\xe1id as na haicearra\xed Ctrl+X/C/V. ","Headings":"Ceannteidil","Heading 1":"Ceannteideal 1","Heading 2":"Ceannteideal 2","Heading 3":"Ceannteideal 3","Heading 4":"Ceannteideal 4","Heading 5":"Ceannteideal 5","Heading 6":"Ceannteideal 6","Preformatted":"R\xe9amhfhorm\xe1idithe","Div":"Deighilt","Pre":"R\xe9amh","Code":"C\xf3d","Paragraph":"Alt","Blockquote":"Athfhriotal","Inline":"Inl\xedne","Blocks":"Blocanna","Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.":"Sa m\xf3d gn\xe1th-th\xe9acs anois. Gream\xf3far \xe1bhar mar ghn\xe1th-th\xe9acs go dt\xed go m\xfachfaidh t\xfa an rogha seo.","Fonts":"Cl\xf3fhoirne","Font sizes":"","Class":"Aicme","Browse for an image":"Brabhs\xe1il le haghaidh \xedomh\xe1","OR":"N\xd3","Drop an image here":"Scaoil \xedomh\xe1 anseo","Upload":"Uasl\xf3d\xe1il","Uploading image":"","Block":"Bloc","Align":"Ail\xednigh","Default":"R\xe9amhshocr\xfa","Circle":"Ciorcal","Disc":"Diosca","Square":"Cearn\xf3g","Lower Alpha":"Alfa Beag","Lower Greek":"Litir Bheag Ghr\xe9agach","Lower Roman":"Litir Bheag R\xf3mh\xe1nach","Upper Alpha":"Alfa M\xf3r","Upper Roman":"Litir Mh\xf3r R\xf3mh\xe1nach","Anchor...":"Ancaire...","Anchor":"","Name":"Ainm","ID":"","ID should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.":"","You have unsaved changes are you sure you want to navigate away?":"T\xe1 athruithe gan s\xe1bh\xe1il ann. An bhfuil t\xfa cinnte gur mhaith leat imeacht amach as seo?","Restore last draft":"Oscail an dr\xe9acht is d\xe9ana\xed","Special character...":"Carachtar speisialta,,,","Special Character":"","Source code":"C\xf3d foinseach","Insert/Edit code sample":"Cuir sampla c\xf3id isteach/in eagar","Language":"Teanga","Code sample...":"Sampla c\xf3id","Left to right":"Cl\xe9-go-deas","Right to left":"Deas-go-cl\xe9","Title":"Teideal","Fullscreen":"L\xe1nsc\xe1ile\xe1n","Action":"Gn\xedomh","Shortcut":"Aicearra","Help":"Cabhair","Address":"Seoladh","Focus to menubar":"F\xf3cas sa bharra roghchl\xe1ir","Focus to toolbar":"F\xf3cas sa bharra uirlis\xed","Focus to element path":"F\xf3cas sa chonair eiliminte","Focus to contextual toolbar":"F\xf3cas sa bharra uirlis\xed comhth\xe9acs\xfail","Insert link (if link plugin activated)":"Cuir nasc isteach (m\xe1 t\xe1 an breise\xe1n naisc ar si\xfal)","Save (if save plugin activated)":"S\xe1bh\xe1il (m\xe1 t\xe1 an breise\xe1n s\xe1bh\xe1la ar si\xfal)","Find (if searchreplace plugin activated)":"Aimsigh (m\xe1 t\xe1 an breise\xe1n cuardaigh ar si\xfal)","Plugins installed ({0}):":"Breise\xe1in shuite\xe1ilte ({0}):","Premium plugins:":"Scothbhreise\xe1in:","Learn more...":"Tuilleadh eolais...","You are using {0}":"T\xe1 t\xfa ag \xfas\xe1id {0}","Plugins":"Breise\xe1in","Handy Shortcuts":"Aicearra\xed \xdas\xe1ideacha","Horizontal line":"L\xedne chothrom\xe1nach","Insert/edit image":"Cuir \xedomh\xe1 isteach/in eagar","Alternative description":"","Accessibility":"Inrochtaineacht","Image is decorative":"","Source":"Foinse","Dimensions":"Tois\xed","Constrain proportions":"Comhr\xe9ir faoi ghlas","General":"Ginear\xe1lta","Advanced":"Casta","Style":"St\xedl","Vertical space":"Sp\xe1s ingearach","Horizontal space":"Sp\xe1s cothrom\xe1nach","Border":"Iml\xedne","Insert image":"Cuir \xedomh\xe1 isteach","Image...":"\xcdomh\xe1...","Image list":"Liosta \xedomh\xe1nna","Resize":"Athraigh m\xe9id","Insert date/time":"Cuir d\xe1ta/am isteach","Date/time":"D\xe1ta/am","Insert/edit link":"Cuir nasc isteach/in eagar","Text to display":"T\xe9acs le taispe\xe1int","Url":"URL","Open link in...":"Oscail nasc in...","Current window":"Fuinneog reatha","None":"Dada","New window":"Fuinneog nua","Open link":"Oscail nasc","Remove link":"Bain an nasc","Anchors":"Ancair\xed","Link...":"Nasc...","Paste or type a link":"Greamaigh n\xf3 cl\xf3scr\xedobh nasc","The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?":"Is seoladh r\xedomhphoist \xe9 an URL a chuir t\xfa isteach. An bhfuil fonn ort an r\xe9im\xedr riachtanach mailto: a chur leis?","The URL you entered seems to be an external link. Do you want to add the required http:// prefix?":"Is nasc seachtrach \xe9 an URL a chuir t\xfa isteach. An bhfuil fonn ort an r\xe9im\xedr riachtanach http:// a chur leis?","The URL you entered seems to be an external link. Do you want to add the required https:// prefix?":" Is nasc seachtrach \xe9 an URL a chuir t\xfa isteach. An bhfuil fonn ort an r\xe9im\xedr riachtanach https:// a chur leis?","Link list":"Liosta nascanna","Insert video":"Cuir f\xedse\xe1n isteach","Insert/edit video":"Cuir f\xedse\xe1n isteach/in eagar","Insert/edit media":"Cuir me\xe1n isteach/in eagar","Alternative source":"Foinse mhalartach","Alternative source URL":"","Media poster (Image URL)":"","Paste your embed code below:":"Greamaigh do ch\xf3d leabaithe th\xedos:","Embed":"Leabaigh","Media...":"Me\xe1n...","Nonbreaking space":"Sp\xe1s neamhbhristeach","Page break":"Briseadh leathanaigh","Paste as text":"Greamaigh mar th\xe9acs","Preview":"R\xe9amhamharc","Print":"","Print...":"Priont\xe1il...","Save":"S\xe1bh\xe1il","Find":"Aimsigh","Replace with":"Ionadaigh le","Replace":"Ionadaigh","Replace all":"Ionadaigh uile","Previous":"","Next":"Ar aghaidh","Find and Replace":"Aimsigh agus Ionadaigh","Find and replace...":"Aimsigh agus ionadaigh...","Could not find the specified string.":"N\xedor aims\xedodh an teaghr\xe1n.","Match case":"C\xe1s-\xedogair","Find whole words only":"","Find in selection":"","Insert table":"Ions\xe1igh t\xe1bla","Table properties":"Air\xedonna an t\xe1bla","Delete table":"Scrios an t\xe1bla","Cell":"Cill","Row":"R\xf3","Column":"Col\xfan","Cell properties":"Air\xedonna na cille","Merge cells":"Cumaisc cealla","Split cell":"Roinn cill","Insert row before":"Ions\xe1igh r\xf3 os a chionn","Insert row after":"Ions\xe1igh r\xf3 faoi","Delete row":"Scrios an r\xf3","Row properties":"Air\xedonna an r\xf3","Cut row":"Gearr an r\xf3","Cut column":"","Copy row":"C\xf3ipe\xe1il an r\xf3","Copy column":"","Paste row before":"Greamaigh r\xf3 os a chionn","Paste column before":"","Paste row after":"Greamaigh r\xf3 faoi","Paste column after":"","Insert column before":"Ions\xe1igh col\xfan ar chl\xe9","Insert column after":"Ions\xe1igh col\xfan ar dheis","Delete column":"Scrios an col\xfan","Cols":"Col\xfain","Rows":"R\xf3nna","Width":"Leithead","Height":"Airde","Cell spacing":"Sp\xe1s\xe1il ceall","Cell padding":"Stu\xe1il ceall","Row clipboard actions":"","Column clipboard actions":"","Table styles":"","Cell styles":"","Column header":"","Row header":"","Table caption":"","Caption":"Fotheideal","Show caption":"","Left":"Ar Chl\xe9","Center":"Sa L\xe1r","Right":"Ar Dheis","Cell type":"Cine\xe1l na cille","Scope":"Sc\xf3ip","Alignment":"Ail\xedni\xfa","Horizontal align":"","Vertical align":"","Top":"Barr","Middle":"L\xe1r","Bottom":"Bun","Header cell":"Cill cheannt\xe1isc","Row group":"Gr\xfapa r\xf3nna","Column group":"Gr\xfapa col\xfan","Row type":"Cine\xe1l an r\xf3","Header":"Ceannt\xe1sc","Body":"Corp","Footer":"Bunt\xe1sc","Border color":"Dath na himl\xedne","Solid":"","Dotted":"","Dashed":"","Double":"","Groove":"","Ridge":"","Inset":"","Outset":"","Hidden":"","Insert template...":"Ions\xe1igh teimpl\xe9ad...","Templates":"Teimpl\xe9id","Template":"Teimpl\xe9ad","Insert Template":"","Text color":"Dath an t\xe9acs","Background color":"Dath an ch\xfalra","Custom...":"Saincheap...","Custom color":"Dath saincheaptha","No color":"Gan dath","Remove color":"","Show blocks":"Taispe\xe1in blocanna","Show invisible characters":"Taispe\xe1in carachtair dhofheicthe","Word count":"","Count":"","Document":"","Selection":"","Words":"","Words: {0}":"Focail: {0}","{0} words":"{0} focal","File":"Comhad","Edit":"Eagar","Insert":"Ions\xe1ig","View":"Amharc","Format":"Form\xe1id","Table":"T\xe1bla","Tools":"Uirlis\xed","Powered by {0}":"\xc1 chumhacht\xfa ag {0}","Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help":"Limist\xe9ar M\xe9ith-Th\xe9acs. Br\xfaigh ALT-F9 le haghaidh roghchl\xe1ir, ALT-F10 le haghaidh barra uirlis\xed, agus ALT-0 le c\xfanamh a fh\xe1il","Image title":"","Border width":"","Border style":"","Error":"","Warn":"","Valid":"","To open the popup, press Shift+Enter":"","Rich Text Area":"","Rich Text Area. Press ALT-0 for help.":"","System Font":"Cl\xf3fhoireann C\xf3rais","Failed to upload image: {0}":"","Failed to load plugin: {0} from url {1}":"","Failed to load plugin url: {0}":"","Failed to initialize plugin: {0}":"","example":"","Search":"","All":"","Currency":"","Text":"","Quotations":"","Mathematical":"","Extended Latin":"","Symbols":"Siombail\xed","Arrows":"","User Defined":"","dollar sign":"","currency sign":"","euro-currency sign":"","colon sign":"","cruzeiro sign":"","french franc sign":"","lira sign":"","mill sign":"","naira sign":"","peseta sign":"","rupee sign":"","won sign":"","new sheqel sign":"","dong sign":"","kip sign":"","tugrik sign":"","drachma sign":"","german penny symbol":"","peso sign":"","guarani sign":"","austral sign":"","hryvnia sign":"","cedi sign":"","livre tournois sign":"","spesmilo sign":"","tenge sign":"","indian rupee sign":"","turkish lira sign":"","nordic mark sign":"","manat sign":"","ruble sign":"","yen character":"","yuan character":"","yuan character, in hong kong and taiwan":"","yen/yuan character variant one":"","Emojis":"","Emojis...":"","Loading emojis...":"","Could not load emojis":"","People":"","Animals and Nature":"","Food and Drink":"","Activity":"","Travel and Places":"","Objects":"","Flags":"","Characters":"","Characters (no spaces)":"","{0} characters":"","Error: Form submit field collision.":"","Error: No form element found.":"","Color swatch":"","Color Picker":"","Invalid hex color code: {0}":"","Invalid input":"","R":"D","Red component":"","G":"U","Green component":"","B":"G","Blue component":"","#":"","Hex color code":"","Range 0 to 255":"","Turquoise":"","Green":"","Blue":"","Purple":"","Navy Blue":"","Dark Turquoise":"","Dark Green":"","Medium Blue":"","Medium Purple":"","Midnight Blue":"","Yellow":"","Orange":"","Red":"","Light Gray":"","Gray":"","Dark Yellow":"","Dark Orange":"","Dark Red":"","Medium Gray":"","Dark Gray":"","Light Green":"","Light Yellow":"","Light Red":"","Light Purple":"","Light Blue":"","Dark Purple":"","Dark Blue":"","Black":"Dubh","White":"","Switch to or from fullscreen mode":"","Open help dialog":"","history":"","styles":"","formatting":"","alignment":"","indentation":"","Font":"Cl\xf3fhoireann","Size":"","More...":"Tuilleadh...","Select...":"","Preferences":"","Yes":"","No":"","Keyboard Navigation":"","Version":"","Code view":"","Open popup menu for split buttons":"","List Properties":"","List properties...":"","Start list at number":"","Line height":"","Dropped file type is not supported":"","Loading...":"","ImageProxy HTTP error: Rejected request":"","ImageProxy HTTP error: Could not find Image Proxy":"","ImageProxy HTTP error: Incorrect Image Proxy URL":"","ImageProxy HTTP error: Unknown ImageProxy error":""});