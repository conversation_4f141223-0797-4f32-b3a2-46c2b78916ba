.top-button {
  display: flex;
  justify-content: space-between;
}

.back-btn {
  background-color: #00253A;
  padding: 5px 20px;
  color: #fff;
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: capitalize;
  border: 2px #25bfff solid;
  border-radius: 20px;
  cursor: pointer;
  text-decoration: none;
}
  
.header-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.subheader {
  width: 80%;
}

form {
  margin-left: 2rem;
  margin-right: 2rem;
}

.date-inputs {
  margin-bottom: 2rem;
}

.input-label {
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: capitalize;
  display: block; 
}

.data-select, .date-input {
  width: 50%;
  padding: 12px 20px;
  margin: 8px 0;
  display: inline-block;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-sizing: border-box;
  font-family: 'Open Sans', Arial;
}

.submit-btn {
  background-color: #00253A;
  padding: 5px 20px;
  color: #fff;
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: capitalize;
  border: 2px #25bfff solid;
  border-radius: 20px;
  cursor: pointer;
  text-decoration: none;
  margin-top: 30px;
  display: block;
  font-family: 'Open Sans', Arial;
}

.back-btn:hover, .submit-btn:hover {
  color: rgba(255, 255, 255, 0.5)
}

.return-info {
  margin: 1rem 3rem 0 3rem;
}
