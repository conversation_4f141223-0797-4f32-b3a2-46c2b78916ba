# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
data/
staticfiles/
media/
*.sqlite3
*.db

# Node modules (if any)
node_modules/
npm-debug.log*

# Logs
logs/
*.log

# Documentation
README.md
docs/

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Other
.env.example
*.md
