import jwt

from .serializers import RegisterSerializer, UserSerializer, ChangePasswordSerializer
from subscriptions.models import Subscription, SubscriberToken
from helpers.image_convert import convert_file
from helpers.permissions.permissions import VerifyToken, check_tokens
from helpers.subscription.subscription_helpers import get_user
from helpers.subscription.chat_token import get_stream_token
from accounts.models import Invite, AccessSet, Access
from accounts.serializers import AccessSetSerializer
from helpers.account_helpers import get_access_set, get_account, get_invite
from events.models import Event
from users.models import CustomUser
# from custom_fields.models import ProfileField
# from roles.models import Role
# from chat.models import Message

from rest_framework import generics, permissions, status, pagination
from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework.views import APIView

from django.conf import settings
from django.contrib.auth import authenticate, login, get_user_model, user_logged_in
from django.contrib.auth.tokens import default_token_generator
from django.http import HttpResponse
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.utils.html import strip_tags
from django.core.mail import send_mail, BadHeaderError
from django.core.exceptions import ValidationError
from django.template.loader import render_to_string
from django_tenants.utils import schema_context
from rest_framework_simplejwt.tokens import RefreshToken
from django.http import Http404 
from django.utils import timezone
from django.shortcuts import redirect, render
from django.db.models import Q

from logs.models import LoginEvent

User = get_user_model()


# Register a user, returns user data and token
def get_tokens_for_user(user):
    refresh = RefreshToken.for_user(user)
    return str(refresh.access_token)


class StandardResultsSetPagination(pagination.PageNumberPagination):
    page_size = 100
    page_size_query_param = 'page_size'
    max_page_size = 1000


class RegisterAPI(generics.GenericAPIView):
    serializer_class = RegisterSerializer
    permission_classes = (permissions.AllowAny,)
    # pagination_class = settings.De
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        token = get_tokens_for_user(user)
        user_logged_in.send(sender=user.__class__, request=request, user=user)
        return Response({
            "user": RegisterSerializer(user, context=self.get_serializer_context()).data,
            "token": token
        }, status=status.HTTP_200_OK)


class AllUsersAPI(generics.ListAPIView):
    serializer_class = UserSerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = StandardResultsSetPagination
    queryset =User.objects.all().order_by('email')
    
def get_event_users(e):
    with schema_context(e.schema_name):
        subs = Subscription.objects.all()
        # event_users = User.objects.filter(subscription__in=subs)
        event_users = list(User.objects.filter(subscription__in=subs).values_list('id', flat=True))
        # print()

    return  event_users


#provide primary key of account and customize get queryset
class AllAccountUsersAPI(generics.ListAPIView):
    serializer_class = UserSerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = StandardResultsSetPagination
    # queryset =User.objects.all().order_by('email')

    def get_queryset(self):
        pk = self.kwargs['pk']
        full_users = User.objects.none()
        account = get_account(id=pk)
        if account:
            for e in account.events.all():
                event_users = get_event_users(e)
                users = User.objects.filter(id__in=event_users)
                full_users = full_users | users
                # full_users = full_users.distinct()
                    
        return full_users.distinct()


class AllAccountTeamMembersAPI(generics.ListAPIView):
    serializer_class = AccessSetSerializer
    permission_classes = (permissions.IsAuthenticated,)
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        pk = self.kwargs['pk']
        account = get_account(id=pk)
        if account:
            return AccessSet.objects.filter(account=account)

# def get_subscriptions(user):
#     events = Event.objects.all()
#     subscriptions = []
#     for event in events:
#         if event.schema_name != 'public':
#             with schema_context(event.schema_name):
#                 try:
#                     subscription = Subscription.objects.get(user=user)
#                     subscription = subscriber_to_json(
#                         subscription, event.schema_name)

#                     print(subscription)
#                     subscriptions.append(subscription)
#                 except Subscription.DoesNotExist:
#                     pass

#     return subscriptions


# Login a user, returns user data and token along with their events
@api_view(['POST'])
@permission_classes([permissions.AllowAny, ])
def authenticate_user(request):
    try:
        email = request.data['email']
        email = email.lower()
        password = request.data['password']
        user = authenticate(email=email, password=password)
        if user:
            try:
                token = get_tokens_for_user(user)
                user.save()
                login(request, user)
                user_logged_in.send(sender=user.__class__, request=request, user=user)
                user_details = {'token': token}
                return Response(user_details, status=status.HTTP_200_OK)
            except Exception as e:
                raise e
        elif User.objects.get(email=email):
            user = User.objects.get(email=email)
            res = {'error': 'This user exists in the system but the credentials are incorrect'}
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
        else:
            res = {'error': 'can not authenticate with the given credentials or the account has been deactivated'}
            return Response(res, status=status.HTTP_403_FORBIDDEN)
    except KeyError:
        res = {'error': 'please provide a email and a password'}
        return Response(res)
    except CustomUser.DoesNotExist:
        res = {'error': 'Account doesn\'t exist'}
        return Response(res, status.HTTP_400_BAD_REQUEST)


# To view and manipulate a single user
class ActiveUserDetail(APIView):
    permission_classes = (permissions.IsAuthenticated, VerifyToken)
    # authentication_class = JSONWebTokenAuthentication
    serializer_class = UserSerializer

    # user profile view, view their information
    def get(self, request, format=None):
        user = request.user
        serializer = UserSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    # change user data
    # DO NOT Change password here, will have another api
    def put(self, request, format=None):
        user = request.user
        serializer = UserSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    # delete a user: will most likely go unused
    def delete(self, request, format=None):
        user = request.user
        user.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)





class UserDetails(APIView):
    permission_classes = (permissions.IsAuthenticated,)
    # permission_classes = (permissions.AllowAny,)
    serializer_class = UserSerializer

    def get_object(self, pk):
        try:
            return User.objects.get(id=pk)
        except User.DoesNotExist as e:
            raise Http404 from e
            

    def get(self, request, pk, format=None, **kwargs):
        user = self.get_object(pk)
        serializer = UserSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        user = self.get_object(pk)
        serializer = UserSerializer(user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, statues=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        user = self.get_object(pk)
        # There are two event based relationships with users:
        #1. profile fields are related to users and
        #2. subscriptions are related to users 

        #PSEUDO CODE: 
        """
        for event in events:
            with event context:
                if subscribers.objects.filter(user=user):
                    delete the subscribers
                if profilefields.objects.filter(user=user):
                    delete the fields
        Then delete the user
        """

        # event = request.tenant
        # with schema_context(event.schema_name):
        #     messages = Message.objects.filter(author=user)
        #     if messages:
        #         for message in messages:
        #             message.delete()
        #     profile_fields = ProfileField.objects.filter(user=user)
        #     if profile_fields:
        #         for profile_field in profile_fields:
        #             profile_field.delete()
        #     subscribers = Subscription.objects.filter(user=user)
        #     if subscribers:
        #         for subscriber in subscribers:
        #             subscriber.delete()

        user.is_active = False
        user.save()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ResetUserPassword(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'users/reset_user_password.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        template_name = 'users/reset_user_password.html'
        user_email = request.POST.get('email')
        password = request.POST.get('password')

        if not user_email:
            message = 'An email must be provided'
        elif not password:
            message = 'A new password must be provided'
        elif len(password) < 3:
            message = 'Passwords must be at least 3 characters'
        else:
            try:
                user = CustomUser.objects.get(email=user_email.lower())
                user.set_password(password)
                user.save()
                message = 'Success!'
            except CustomUser.DoesNotExist:
                message = f'No user found with the email: {user_email}'

        context = {'message': message}
        return render(request, template_name, context)


# password change for already logged in user
@api_view(['PUT'])
@permission_classes([permissions.AllowAny, ])
def change_password(request):
    user = request.user
    serializer = ChangePasswordSerializer(
        user, data=request.data, partial=True)
    if serializer.is_valid():
        serializer.save()
        return Response(serializer.data, status=status.HTTP_200_OK)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# password reset: send email
@api_view(['POST'])
@permission_classes([permissions.AllowAny, ])
def password_reset(request):
    try:
        email = request.data['email']
        email = email.lower()
        hostname = request.data['hostname']
        protocol = request.data['protocol']
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            user = None

        if user:
            subject = "Password Reset Requested"
            email_template_name = "forgot_password.html"
            token = default_token_generator.make_token(user)
            context = {
                'email': email,
                'domain': hostname,
                'first_name': user.first_name,
                'uid': urlsafe_base64_encode(force_bytes(user.pk)),
                'token': token,
                'protocol': protocol,
                "user": user
            }
            html_message = render_to_string(email_template_name, context)
            email = strip_tags(html_message)
            try:
                send_mail(subject, email, 'Matchbox Support <<EMAIL>>', [user.email], fail_silently=False, html_message=html_message)
            except BadHeaderError:
                return HttpResponse('Invalid header found.')
            message = 'A message with reset password instructions has been sent to your inbox.'
            return Response(message, status=status.HTTP_200_OK)

        else:
            res = {'error': 'user does not exist'}
            return Response(res, status=status.HTTP_404_NOT_FOUND)
    except KeyError:
        res = {'error': 'please provide a valid email'}
        return Response(res, status=status.HTTP_400_BAD_REQUEST)


# password reset confirm
@api_view(['POST'])
@permission_classes([permissions.AllowAny, ])
def password_reset_confirm(request, **kwargs):
    try:
        new_password1 = request.data['password']
        new_password2 = request.data['password2']
        uid = kwargs.get('uidb64')
        token = kwargs.get('token')
        if new_password1 != new_password2:
            res = {'error': 'passwords do not match'}
            return Response(res, status=status.HTTP_400_BAD_REQUEST)
        try:
            user_id = force_str(urlsafe_base64_decode(uid))
            user = User._default_manager.get(pk=user_id)
            if default_token_generator.check_token(user, token):
                user.set_password(new_password1)
                user.save()
                email = user.email
                subject = "Password Reset Confirmation"
                email_template_name = "password_email_confirm.html"
                context = {
                    'email': email,
                    'first_name': user.first_name,
                    "user": user
                }
                html_message = render_to_string(email_template_name, context)
                email = strip_tags(html_message)
                try:
                    send_mail(subject, email, 'Matchbox Support <<EMAIL>>', [user.email], fail_silently=False, html_message=html_message)
                except BadHeaderError:
                    return HttpResponse('Invalid header found.')
                return Response({
                    "detail": ("Password has been reset with the new password."),
                    "email": user.email
                })
            else:
                res = {'error': 'invalid token'}
                return Response(res, status=status.HTTP_403_FORBIDDEN)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist) as e:
            raise ValidationError({'uid': ['Invalid value']}) from e
    except KeyError:
        res = {'error': 'please provide passwords and tokens'}
        return Response(res)


@api_view(['PUT'])
@permission_classes([permissions.IsAuthenticated, VerifyToken])
def upload_avatar(request):
    user = request.user
    try:
        base64_string = request.data['base64']
        file_name = request.data['filename']
        image = convert_file(base64_string, file_name)
        serializer = UserSerializer(user, data={'avatar': image}, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    except KeyError:
        res = {'error': 'please provide base64 and filename'}
        return Response(res)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def get_events(request, pk):
    try:
        user = User.objects.get(id=pk)
    except Exception:
        res = {'error': 'error finding user'}
        return Response(res)
    subs =[]
    events = Event.objects.all()
    for event in events:
        if event.schema_name == 'public':
            continue
        with schema_context(event.schema_name):
            if Subscription.objects.filter(user=user):
                subs.append(event.name)
    res = {'events': subs}
    return Response(res, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.AllowAny, ])
def authenticate_admin_user(request):
    try:
        email = request.data['email']
        email = email.lower()
        password = request.data['password']
        user = authenticate(email=email, password=password)
        if user:
            if user.is_staff or user.is_admin:
                token = get_tokens_for_user(user)
                user_details = {'token': token}
                user_logged_in.send(sender=user.__class__, request=request, user=user)
                return Response(user_details, status=status.HTTP_200_OK)
            else:
                res = {'error': 'This user does not have permission to access the site'}
                return Response(res, status.HTTP_401_UNAUTHORIZED)
        elif User.objects.get(email=email):
            user = User.objects.get(email=email)
            res = {
                'error': 'This user exists in the system but the credentials are incorrect'}
            return Response(res, status.HTTP_401_UNAUTHORIZED)
        else:
            res = {'error': 'can not authenticate with the given credentials or the account has been deactivated'}
            return Response(res, status.HTTP_401_UNAUTHORIZED)
    except KeyError:
        res = {'error': 'please provide a email and a password'}
        return Response(res)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated, ])
def send_invite(request):
    # print('SEND INVITE')
    try:
        event = request.tenant
        domain_url = event.domain_url
        frontend_domain = ''
        sub_domain = ''
        if 'localhost' in domain_url:
            domain_url = f'{domain_url}:8002'
            frontend_domain = 'localhost:3000'
        if 'mbvs-server-v2-dev2.us-east-1.elasticbeanstalk.com' in event.domain_url:
            frontend_domain = 's3-website-us-east-1.amazonaws.com'
            sub_domain = event.frontend_sub_domain
        if 'mbvs-server-development.com' in event.domain_url:
            frontend_domain = 'matchboxvirtualdevelopment.com'
            sub_domain = event.frontend_sub_domain
        if 'mbvs-server-staging.com' in event.domain_url:
            frontend_domain = 'matchboxvirtualstaging.com'
            sub_domain = event.frontend_sub_domain
        if 'mbvs-server.com' in event.domain_url:
            frontend_domain = 'matchboxvirtualspaces.com'
            sub_domain = event.frontend_sub_domain

        email = request.data['email']
        email = email.lower()
        accesses = request.data['accesses']
        account_id = request.data['account']
        #if role is admin, make it everything
        role = request.data['role']
        account = get_account(id=account_id)
        if not account:
            res = {'error': 'Account not found'}
            return Response(res, status=status.HTTP_404_NOT_FOUND)

        invite = Invite.objects.create(email=email, account=account, sent_by=request.user, role=role)
        if role == 'ADMIN':
            accesses = list(Access.objects.values_list('name', flat=True))
        invite.accesses.set(accesses)
        invite.save()
        new = True

        user = get_user(email)
        if user:
            new=False
        payload = {
            'email': email,
            'account': str(account.id),
            'invite': str(invite.id),
            'frontend_domain': frontend_domain,
            'sub_domain': sub_domain
        }
        token = jwt.encode(payload, settings.SECRET_KEY)
        #TODO: change
        subject = "Invitation to account"
        email_template_name = "send_invite.html"
        context = {
            'domain_url': domain_url,
            'email': email,
            'token': token,
            'new': str(new).lower(),
            'account': account,
            'sent_by': request.user,
        }
        # print(context)
        html_message = render_to_string(email_template_name, context)
        message = strip_tags(html_message)
        try:
            send_mail(subject, message, 'Matchbox Support <<EMAIL>>', [email], fail_silently=False, html_message=html_message)
        except BadHeaderError:
            return HttpResponse('Invalid header found.')
        res = 'An invite has been sent'
        invite.last_sent = timezone.now()
        invite.save()
        return Response(res, status=status.HTTP_200_OK)

    except KeyError:
        res = {'error': 'please provide a valid email'}
        return Response(res, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.AllowAny, ])
def accept_invite(request):
    # print('ACCEPT INVITE')
    payload = request.GET.get('token')
    try:
        token = jwt.decode(payload, settings.SECRET_KEY, algorithms=["HS256"])

    except jwt.InvalidSignatureError:
        res = {'error': 'Signature Verification Failed'}
        return Response(res, status=status.HTTP_400_BAD_REQUEST)
    except Exception:
        res = {'error': 'An unknown error occured'}
        return Response(res, status=status.HTTP_400_BAD_REQUEST)
    
    frontend_domain = token['frontend_domain']
    sub_domain = token['sub_domain']
    invite = token['invite']
    email = token['email']
    account_id = token['account']
    account = get_account(id=account_id)

    if not account:
        res = {'error': 'Account not found'}
        return Response(res, status=status.HTTP_404_NOT_FOUND)
    invitation = get_invite(id=invite, account=account, email=email)
    if not invitation:
        res = {'error': 'Invite not found'}
        return Response(res, status=status.HTTP_400_BAD_REQUEST)
    
    #if no user then error, user needs to be created first
    user = get_user(email)
    if not user:
        res = {'error': 'User not found'}
        return Response(res, status=status.HTTP_400_BAD_REQUEST)
    user.is_admin = True
    user.save()
    access_set = get_access_set(user=user, account=account)
    if not access_set:
        try:
            access_set= AccessSet.objects.create(user=user, account=account, role=invitation.role)  
        except Exception:
            res = {'error': 'Could not create access set'}
            return Response(res, status=status.HTTP_400_BAD_REQUEST)      
    access_set.role = invitation.role
    access_set.accesses.set(invitation.accesses.all())
    access_set.save()
    invitation.status = 'ACCEPTED'
    invitation.save()
    # token = get_tokens_for_user(user)
    # message = {'detail': 'Invite accepted','token': token}
    # return Response(message, status=status.HTTP_200_OK)
    if 'localhost' not in frontend_domain:
        response = redirect(f'http://{sub_domain}.{frontend_domain}/login')
    else:
        response = redirect(f'http://{frontend_domain}/login')
    return response

# create magic link: send email
@api_view(['POST'])
@permission_classes([permissions.AllowAny, ])
def create_magic_link(request):
    try:
        email = request.data['email']
        email = email.lower()
        event_name = request.data['event_name']
        redirect_to = request.data['redirect_to']
        dont_send_to_user = request.data.get('dont_send_to_user', False)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            user = None

        if user:
            subject = f"Your quick access link to {event_name}"
            email_template_name = "magic_link.html"
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            
            magic_link = None
            res = {
                'message': 'A message with magic link login instructions has been sent to your inbox.',
            }
            if dont_send_to_user == True:
                magic_link = f"{redirect_to}/verify_magic_link/{uid}/{token}/"
            else:
                context = {
                    'email': email,
                    'redirect_to': redirect_to,
                    'first_name': user.first_name,
                    'uid': uid,
                    'token': token,
                    "user": user,
                    "event_name": event_name
                }
                html_message = render_to_string(email_template_name, context)
                email = strip_tags(html_message)
                
                try:
                    send_mail(subject, email, 'Matchbox Support <<EMAIL>>', [user.email], fail_silently=False, html_message=html_message)
                except BadHeaderError:
                    print('Something went wrong sending the magic link')


            if magic_link is not None:
                res['magic_link'] = magic_link
                res['message'] = 'Magic link generated successfully.'
                
            return Response(res, status=status.HTTP_200_OK)

        else:
            res = {'error': 'user does not exist'}
            return Response(res, status=status.HTTP_404_NOT_FOUND)
    except KeyError as e:
        print(e)
        res = {'error': 'please provide a valid email'}
        return Response(res, status=status.HTTP_400_BAD_REQUEST)


# verify magic link token
@api_view(['POST'])
@permission_classes([permissions.AllowAny, ])
def verify_magic_link(request, **kwargs):
    try:
        uid = kwargs.get('uidb64')
        token = kwargs.get('token')

        try:
            user_id = force_str(urlsafe_base64_decode(uid))
            user = User._default_manager.get(pk=user_id)
            if default_token_generator.check_token(user, token):
                user_token = get_tokens_for_user(user)

                try:
                    subscriber = Subscription.objects.get(user=user)
                except Exception:
                    res = {
                        'error': 'There is no subscriber for this user for this event',
                        'token': user_token
                    }
                    return Response(res, status=status.HTTP_404_NOT_FOUND)
            
                if subscriber:
                    try:
                        event = request.tenant
                        check_tokens(subscriber, event.num_logins)
                        SubscriberToken.objects.create(
                            subscriber=subscriber, token=user_token)
                        LoginEvent.objects.create(subscriber=subscriber)
                        subscriber.save()
                        user.save()
                        stream_token = get_stream_token(subscriber)
                        subscriber_details = {
                            'token': user_token,
                            'stream_token': stream_token
                        }
                        user_logged_in.send(sender=user.__class__,
                                            request=request, user=user)
                        return Response(subscriber_details, status=status.HTTP_200_OK)

                    except Exception as e:
                        raise e

                res = {'error': 'this should not happend, please contact support'}
                return Response(res)
            else:
                res = {'error': 'invalid token'}
                return Response(res, status=status.HTTP_403_FORBIDDEN)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist) as e:
            raise ValidationError({'uid': ['Invalid value']}) from e
    except KeyError:
        res = {'error': 'please use a valid magic link'}
        return Response(res)

@api_view(['POST'])
@permission_classes([permissions.AllowAny, ])
def email_exists(request):
    try:
        email = request.data['email']
        email = email.lower()
        if User.objects.get(email=email):
            user = User.objects.get(email=email)
            res = {
                'message': 'you can authenticate with this user'
            }
            return Response(res, status=status.HTTP_200_OK)

        else:
            res = {
                'error': 'can not authenticate with the given email or the account has been deactivated'
            }
            return Response(res, status=status.HTTP_403_FORBIDDEN)
        
    except KeyError:
        res = {'error': 'please provide a email'}
        return Response(res)
    except User.DoesNotExist:
        res = {'error': 'email does not exist'}
        return Response(res, status=status.HTTP_404_NOT_FOUND)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def list_users(request):
    filter_text = request.GET.get('text', '')

    if filter_text:
        users = User.objects.filter(
            Q(first_name__icontains=filter_text) | Q(email__icontains=filter_text)
        ).distinct()
    else:
        users = User.objects.all()

    paginator = StandardResultsSetPagination()
    result_page = paginator.paginate_queryset(users, request)
    serializer = UserSerializer(result_page, many=True)
    return paginator.get_paginated_response(serializer.data)

