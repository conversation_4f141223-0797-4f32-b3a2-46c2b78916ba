from django.urls import include, path
from django.contrib import admin
# from rest_framework.schemas import get_schema_view
# from rest_framework import permissions
from django.views.generic import TemplateView
from custom_fields.urls import custom_fields_router
from django.contrib.auth import views as auth_views
from subscriptions import backend_views
from django.contrib.auth.decorators import user_passes_test
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.staticfiles.storage import staticfiles_storage
from django.views.generic.base import RedirectView
from django.conf import settings
from django.conf.urls.static import static
# import debug_toolbar


urlpatterns = [
    # path('admin/postgres-metrics/', include('postgres_metrics.urls')),
    path('admin/', admin.site.urls, name='admin'),
    path('home/', user_passes_test(lambda u: u.is_superuser)(TemplateView.as_view(template_name="home.html")), name='home'),
    path('', backend_views.base, name='base'),
    path('delete/<pk>/', backend_views.delete_subscriber, name='sub_delete'),
    path('delete_all/', backend_views.delete_all_subscribers, name='sub_delete_all'),
    path('login_client/', auth_views.LoginView.as_view(), name='login_client'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('export/', staff_member_required(TemplateView.as_view(template_name="export/export.html"), login_url='/login_client/'), name='export'),
    path('import/',  staff_member_required(TemplateView.as_view(template_name="import/import.html"), login_url='/login_client/'), name='import'),
    path('event_upload/',  staff_member_required(TemplateView.as_view(template_name="event_upload/event_upload.html"), login_url='/login_client/'), name='event_upload'),
    path('event_download/',  staff_member_required(TemplateView.as_view(template_name="event_download/event_download.html"), login_url='/login_client/'), name='event_download',),
    path('event_data/',  staff_member_required(TemplateView.as_view(template_name="event_data/event_data_dash.html"), login_url='/login_client/'), name='event_data'),
    path('user_management/',  staff_member_required(TemplateView.as_view(template_name="users/user_management.html"), login_url='/login_client/'), name='user_management'),
    path('', include('expohall.data_urls')),
    path('', include('custom_fields.backend_urls')),
    path('', include('auditorium.backend_urls')),
    path('', include('users.urls')),
    path('', include('roles.urls')),
    path('', include('subscriptions.urls')),
    path('', include('spaces.urls')),
    path('', include('chat.urls')),
    path('', include('timeTriggers.urls')),
    path('', include('events.urls')),
    path('', include('credits.urls')),
    path('', include('polls.urls')),
    path('<str:space>/api/', include('auditorium.urls')),
    path('<str:space>/api/', include('custom_page.urls')),
    # path('<str:space>/api/', include('expohall.urls')),
    path('', include('expohall.urls')),
    path('', include(custom_fields_router.urls)),
    path('', include('logs.urls')),
    path('', include('zoom.urls')),
    path('', include('public_resources.urls')),
    path('', include('tags.urls')),
    path('', include('accounts.urls')),
    #path('', include(events_router.urls)),
    #path('', include(timed_events_router.urls)),
    path('', include('confirmation_page.urls')),
    
    path(
        'matchbox.ico',
        RedirectView.as_view(url=staticfiles_storage.url('matchbox.ico')),
    ),

    # API Documentation
    # path('openapi/', get_schema_view(
    #     title="Your Project",
    #     description="API for all things …",
    #     version="1.0.0",
    #     permission_classes=(permissions.IsAuthenticated,)
    # ), name='openapi-schema'),
    # path('swagger-ui/', staff_member_required(TemplateView.as_view(
    #     template_name='swagger-ui.html',
    #     extra_context={'schema_url': 'openapi-schema'}
    # ), login_url='/login_client/'), name='swagger-ui'),

]

# Serve static files during development
if settings.DEBUG or not hasattr(settings, 'STATICFILES_STORAGE') or 'whitenoise' not in settings.STATICFILES_STORAGE.lower():
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
# Debug
# if settings.DEBUG:
#     urlpatterns += [
#         re_path(r'__debug__/', include(debug_toolbar.urls)),
#     ]