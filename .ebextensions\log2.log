
2025/07/05 06:08:33.145444 [INFO] Running command: /usr/sbin/nginx -t -c /var/proxy/staging/nginx/nginx.conf
2025/07/05 06:08:33.166631 [INFO] nginx: [emerg] "location" directive is not allowed here in /var/proxy/staging/nginx/conf.d/proxy.conf:8
nginx: configuration file /var/proxy/staging/nginx/nginx.conf test failed

2025/07/05 06:08:33.166647 [ERROR] An error occurred during execution of command [app-deploy] - [start proxy with new configuration]. Stop running the command. Error: copy proxy conf from staging failed with error validate nginx configuration failed with error Command /usr/sbin/nginx -t -c /var/proxy/staging/nginx/nginx.conf failed with error exit status 1. Stderr:nginx: [emerg] "location" directive is not allowed here in /var/proxy/staging/nginx/conf.d/proxy.conf:8
nginx: configuration file /var/proxy/staging/nginx/nginx.conf test failed
