from helpers.imports.universal_helpers import get_default_false, validate_datetime
from helpers.imports.spaces_helpers import get_spaces
from helpers.imports.auditorium_helpers import get_auditoriums
from helpers.imports.timed_events_helpers import create_date_time, get_or_create_timed_event, get_or_create_action, get_fire_after, get_or_create_notification_btn, get_or_create_notification_action, get_or_create_transition_action, get_or_create_poll_action, get_or_create_ex_transition_action

from rest_framework.views import APIView
from rest_framework import permissions
from django.contrib import messages
from django.shortcuts import render
import io, csv


class TimeTrigActionUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'event_upload/import_actions.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_actions.html'
        try:
            csv_file = request.FILES['eventfile']
        except:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')

        imported_date_times = []
        imported_timed_events = []
        imported_actions = []
        imported_notification_buttons = []
        imported_notification_actions = []
        imported_transitions = []
        imported_ex_transitions = []
        imported_polls = []
        errors = []

        reader = csv.DictReader(file)
        for row in reader:
            #getting the params
            space_names = row['space_names']
            aud_names = row['aud_names']
            action_name = row['action_name']
            action_description = row['action_description']
            action_constraint = row['action_constraint']
            start_date_time = row['start_date_time']
            late_date_time = row['late_date_time']
            timed_event_name = row['timed_event_name']
            timed_event_description = row['timed_event_description']
            fire_after = row['fire_after']
            na_label = row['na_label']
            notification_btn_text = row['notification_btn_text']
            notification_btn_type = row['notification_btn_type']
            notification_header = row['notification_header']
            notification_paragraph = row['notification_paragraph']
            notification_allow_close = get_default_false(row['notification_allow_close'])
            ta_label = row['ta_label']
            transition_redirect_header = row['transition_redirect_header']
            transition_redirect_url = row['transition_redirect_url']
            pa_label = row['pa_label']
            poll_header = row['poll_header']
            poll_link = row['poll_link']
            track_users = get_default_false(row['track_poll_users'])
            eta_label = row['eta_label']
            ex_transition_redirect_header = row['ex_transition_redirect_header']
            ex_transition_redirect_url = row['ex_transition_redirect_url']

            start_date_time = validate_datetime(start_date_time)
            late_date_time = validate_datetime(late_date_time)

            if start_date_time == False:
                errors.append(f"{timed_event_name} - {action_name}'s start_date_time must be a valid date")
            if late_date_time == False:
                errors.append(f"{timed_event_name} - {action_name}'s late_date_time must be a valid date")

            #Check date times
            if start_date_time and late_date_time and start_date_time <= late_date_time:
                date_time = create_date_time(start_date_time, late_date_time)
                if date_time and date_time not in imported_date_times:
                    imported_date_times.append(date_time)
            else:
                date_time = False

            if date_time == False:
                errors.append(f"Could not create the DateTime, TimedEvent, and Action objects for {timed_event_name} - {action_name}")
                continue

            #Check Timed Event
            #retrieves timed event and updates the time if it is there
            timed_event = get_or_create_timed_event(timed_event_name, date_time)
            if timed_event and timed_event not in imported_timed_events:
                imported_timed_events.append(timed_event)
            elif not timed_event:
                errors.append(f"Could not create the timed_event object of {timed_event_name} - {action_name}")
                continue

            #add space, aud, description
            if space_names != '':
                space_list = space_names.split(' \\ ')    
                new_spaces = get_spaces(space_list)
                if new_spaces.count() != len(space_list):
                    errors.append(f"For {timed_event_name} - {action_name} some Spaces may not have been added or were cleared")
                timed_event.spaces.set(new_spaces)
            
            if aud_names != '':
                auditorium_list = aud_names.split(' \\ ')
                new_auds = get_auditoriums(auditorium_list)
                if new_auds.count() != len(auditorium_list):
                    errors.append(f"For {timed_event_name} - {action_name} some Auditoriums may not have been added or were cleared")
                timed_event.auditoriums.set(new_auds)

            if timed_event_description not in [timed_event.description, '']:
                timed_event.description = timed_event_description
                timed_event.save()

            #Action
            action = get_or_create_action(action_name, timed_event, date_time)
            if not action:
                errors.append(f"Could not create the action object of {timed_event_name} - {action_name}")
                continue
            action.timed_event = timed_event

            action.save()
            if action_description not in [action.description, '']:
                action.description = action_description
                action.save()
            if action and action_constraint in ['running', 'executed', 'expired']:
                action.constraint = action_constraint
                action.save()
            if action not in imported_actions:
                imported_actions.append(action)

            #fire after
            if fire_after != '':
                fire_after_action = get_fire_after(fire_after)
                if not fire_after_action:
                    errors.append(f"{timed_event_name} - {action_name}'s fired_after field cannot be added")
                elif fire_after != action.fire_after:
                    action.fire_after = fire_after_action
                    action.save()


            if na_label != '' and ta_label == '' and pa_label == '' and eta_label == '' and action:
                notification_act = get_or_create_notification_action(action, na_label)
                if not notification_act:
                    errors.append(f"{timed_event_name} - {action_name}'s notification action could not be added")
                    continue
                notification_btn = get_or_create_notification_btn(notification_btn_text, notification_btn_type)
                if not notification_btn:
                    errors.append(f"{timed_event_name} - {action_name}'s button could not be added")
                    continue
                notification_act.header = notification_header
                notification_act.paragraph = notification_paragraph
                notification_act.proceed_button = notification_btn
                notification_act.allow_close = notification_allow_close
                notification_act.save()
                imported_notification_actions.append(notification_act)
                imported_notification_buttons.append(notification_btn)

            if ta_label != '' and na_label == '' and pa_label == '' and eta_label == '' and action:
                transition_act = get_or_create_transition_action(action, ta_label)
                if not transition_act:
                    errors.append(f"{timed_event_name} - {action_name}'s Transition Action cannot be added")
                    continue
                transition_act.header = transition_redirect_header
                transition_act.redirect_to = transition_redirect_url
                transition_act.save()
                imported_transitions.append(transition_act)

            if pa_label != '' and na_label == '' and ta_label == '' and eta_label == '' and action:
                poll_action = get_or_create_poll_action(action, pa_label)
                if not poll_action:
                    errors.append(f"{timed_event_name} - {action_name}'s Poll Action cannot be added")
                    continue
                poll_action.header = poll_header
                poll_action.poll_link = poll_link
                poll_action.track_users = track_users
                poll_action.save()
                imported_polls.append(poll_action)

            if eta_label != '' and na_label == '' and ta_label == '' and pa_label == '' and action:
                ex_transition_act = get_or_create_ex_transition_action(action, eta_label)
                if not ex_transition_act:
                    errors.append(f"{timed_event_name} - {action_name}'s Ex Transition Action cannot be added")
                    continue
                ex_transition_act.header = ex_transition_redirect_header
                ex_transition_act.redirect_to = ex_transition_redirect_url
                ex_transition_act.save()
                imported_ex_transitions.append(ex_transition_act)

            if na_label != '' and ta_label != '' or na_label != '' and pa_label != '' or na_label != '' and eta_label != '' or ta_label != '' and pa_label != '' or ta_label != '' and eta_label != '' or pa_label != '' and eta_label != '':
                errors.append(f"{timed_event_name} - {action_name} can only have 1 Action Event")

            if na_label == '' and ta_label == '' and pa_label == '' and eta_label == '':
                errors.append(f"The Action Event of {timed_event_name} - {action_name} was not selected")

        context = {
            'message': 'Upload Completed!',
            'imported_date_times': imported_date_times,
            'imported_timed_events': imported_timed_events,
            'imported_actions': imported_actions,
            'imported_notification_buttons': imported_notification_buttons,
            'imported_notification_actions': imported_notification_actions,
            'imported_transitions': imported_transitions,
            'imported_polls': imported_polls,
            'imported_ex_transitions': imported_ex_transitions,
            'errors': errors,
        }
        return render(request, template_name, context)

