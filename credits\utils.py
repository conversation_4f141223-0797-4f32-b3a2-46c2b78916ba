from .models import CreditDefinition, CreditRecord, Criteria, CompletedCriteria
from helpers.tenants.tenant_helpers import get_current_tenant
from django.utils import timezone
import datetime

def show_criteria(list):
    values = []
    for value in list:
        values.append(value.criteria.name)
    string = " \\ ".join(values)
    # if arg is first_name: return the first string before space

    return string

def get_credit_definition(name):
    try:
        return CreditDefinition.objects.get(name=name)
    except:
        return False

def get_or_create_record(credit_definition, subscription):
    try:
        return CreditRecord.objects.get(credit_definition=credit_definition, subscriber=subscription)
    except CreditRecord.DoesNotExist:
        return CreditRecord.objects.create(credit_definition=credit_definition, subscriber=subscription)
    except:
        return False

def get_criteria(name, definition):
    try:
        Criteria.objects.get(name=name, credit_definition=definition)
    except:
        return False

def get_or_create_completed_criteria(criteria_list, record):
    added_criteria = []
    criteria = Criteria.objects.filter(name__in=criteria_list, credit_definition=record.credit_definition)
    completed = CompletedCriteria.objects.filter(credit_record=record)
    for c in criteria:
        if not completed.filter(criteria=c):
            new_complete = CompletedCriteria.objects.create(criteria=c, credit_record=record)
            new_complete.save()
            calculate_approval(record)
            added_criteria.append(f'{c}: {record.subscriber.user.email}')
    return added_criteria

def get_credits_earned(record):
    earned_credits = 0
    max_credits = record.credit_definition.max_credits or 1000
    min_credits = record.credit_definition.min_credits or -1
    used_criteria = [] 
    for completed in record.completed_criteria.all():
        if completed.criteria.name not in used_criteria:
            earned_credits += completed.criteria.credit_value
            used_criteria.append(completed.criteria.name)
    highest_earned = min(earned_credits, max_credits)
    return max(min_credits, highest_earned)

#in future be able to add own template fields
def get_template_data(record):
    event = get_current_tenant()
    template_data = {}
    template_data['user_name'] = f'{record.subscriber.user.first_name} {record.subscriber.user.last_name} '
    template_data['session_name'] = f'{record.credit_definition.name} '
    template_data['date'] = f'{str(datetime.date.today())} '
    template_data['date_approved'] = f'{record.date_approved} '
    template_data['last modified'] = f'{record.last_criteria_added_date} '
    template_data['event'] = event.name
    template_data['credits'] = f'{str(get_credits_earned(record))} '
    return template_data

#Questions: if youre already approved does it re-evaluate and update?
#if completed is not empty and partial credits, if not approved and all required criteria completed 
    # then approve and update time, otherwise leave it 
def calculate_approval(record):
    record.last_criteria_added_date = timezone.now()
    completed_criteria = record.completed_criteria.all()
    credit_definition = record.credit_definition
    definition_criteria = record.credit_definition.criteria.all()
    if not record.approved:
        if credit_definition.allow_partial_credits:
            #check that record had completed all required criteria
            for c in definition_criteria.filter(required=True):
                if not completed_criteria.filter(criteria=c).exists():
                    return record
        else:
            for c in definition_criteria.filter():
                if not completed_criteria.filter(criteria=c).exists():
                    return record
        record.approved = True
        record.date_approved = timezone.now()

    record.save()
    return record

def join_with_backslash(values):
    return ' \\ '.join(values)

            

    