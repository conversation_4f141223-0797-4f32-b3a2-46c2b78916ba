from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from custom_fields.serializers import ExternalProfileFieldSerializer, KeyValueProfileFieldSerializer
User = get_user_model()

# user serializer


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = '__all__'
        # exclude = ['password']

    def update(self, instance, validated_data):
        instance.id = validated_data.get('id', instance.id)
        instance.email = validated_data.get('email', instance.email)
        instance.first_name = validated_data.get('first_name', instance.first_name)
        instance.last_name = validated_data.get('last_name', instance.last_name)
        instance.screen_name = validated_data.get('screen_name', instance.screen_name)
        instance.avatar = validated_data.get('avatar', instance.avatar)
        #instance.show = validated_data.get('show', instance.show)
        if validated_data.get('avatar'):
            schema = instance.avatar.url.split('/')[4]
            instance.avatar_schema = schema
        instance.save()
        return instance


class FullUserSerializer(serializers.ModelSerializer):
    profile_fields = ExternalProfileFieldSerializer(many=True, required=False)

    class Meta:
        model = User
        exclude = ['password', 'is_superuser', 'is_staff', 'is_active',
                   'groups', 'user_permissions', 'avatar', 'avatar_schema']

class UserDataSerializer(serializers.ModelSerializer):
    profile_fields = KeyValueProfileFieldSerializer(many=True, required=False)

    class Meta:
        model = User
        exclude = ['password', 'is_superuser', 'is_staff', 'is_active',
                   'groups', 'user_permissions', 'avatar', 'avatar_schema']

    def to_representation(self, instance):
        fields = {}
        ret = super().to_representation(instance)
        prof_fields = ret['profile_fields'] 
        for dict in prof_fields:
            for k,v in dict.items():
                fields.setdefault(k, v)
        ret['profile_fields'] = fields
        return ret
        

# Register Serializer


class RegisterSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'email', 'first_name', 'last_name',
                  'screen_name', 'avatar', 'password')
        extra_kwargs = {'password': {'write_only': True}}

    # creates user using user manager
    def create(self, validated_data):
        return User.objects.create_user(**validated_data)

class UserListSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'email', 'first_name', 'last_name',
                  'screen_name', 'avatar', 'avatar_schema')

    # creates user using user manager
    def create(self, validated_data):
        return User.objects.create_user(**validated_data)

class ChangePasswordSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True)
    password2 = serializers.CharField(write_only=True, required=True)
    old_password = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = User
        fields = ('old_password', 'password', 'password2')

    def validate(self, data):
        if data['password'] != data['password2']:
            raise ValidationError(
                {"password": "Password fields didn't match."})

        return data

    def validate_old_password(self, value):
        # user = self.context['request'].user
        user = self.instance
        if not user.check_password(value):
            raise ValidationError(
                {"old_password": "Old password is not correct"})
        return value

    def update(self, instance, validated_data):
        instance.set_password(validated_data['password'])
        instance.save()

        return instance
