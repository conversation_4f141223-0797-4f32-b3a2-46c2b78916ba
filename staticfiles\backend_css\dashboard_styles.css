.top-button {
    display: flex;
    justify-content: space-between;
}

.back-btn, .logout-btn {
    padding: 5px 20px;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    border: 2px #25bfff solid;
    border-radius: 20px;
    cursor: pointer;
    text-decoration: none;
}

.header {
    text-align: center;
}

.subheader, .section-header {
    text-align: center;
}

.section-description {
    text-align: center;
    font-size: 1rem;
    margin: 0 0 1rem 0;
}

.links, .admin-link {
    margin-top: 2rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
}

.link, .admin {
    align-items: center;
    justify-content: center;
    width: 25%;
    margin-left: 1rem;
    margin-bottom: 1.75rem;
    display:inline-flex;
    flex-direction: column;
    padding: 0.3rem 1rem;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    border: 0.1rem #25bfff solid;
    border-radius: 2rem;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    font-size: 1rem;
}

.admin {
    border: 2px #25bfff solid;
    margin-bottom: 0px;
}

.section-header-container {
    margin-bottom: 0;
}

.old-link {
    align-items: center;
    justify-content: center;
    width: 20%;
    margin-left: 1rem;
    margin-bottom: 1.75rem;
    display:inline-flex;
    flex-direction: column;
    padding: 0.3rem 1rem;
    color: #fff;
    font-weight: bold;
    text-transform: capitalize;
    border: 0.1rem #25bfff solid;
    border-radius: 2rem;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    font-size: .85rem;
}

.logout:hover, .link:hover, .admin:hover {
    color: rgba(255, 255, 255, 0.5)
}