from .models import CompletedCriteria, WatchSessionCriteria, AttendSessionCriteria, Criteria
from rest_framework import serializers
import datetime
from django.utils import timezone


class CompletedCriteriaSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompletedCriteria
        fields = "__all__"


class WatchSessionCriteriaSerializer(serializers.ModelSerializer):
    class Meta:
        model = WatchSessionCriteria
        fields = "__all__"

class AttendSessionCriteriaSerializer(serializers.ModelSerializer):
    class Meta:
        model = AttendSessionCriteria
        fields = "__all__"

class CriteriaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Criteria
        fields = "__all__"
    
    def check_times(self, criteria):
        # time_1 = datetime.datetime(2002, 2, 2).replace(tzinfo=timezone.utc).astimezone(tz=None)
        time_2 = datetime.datetime(2060, 2, 2).replace(tzinfo=timezone.utc).astimezone(tz=None)
        now = timezone.now()
        # if criteria.time_1:
        #     time_1 = criteria.time_1.replace(tzinfo=timezone.utc).astimezone(tz=None)
        if criteria.time_2:
            time_2 = criteria.time_2.replace(tzinfo=timezone.utc).astimezone(tz=None)
        # if now >= time_1 and now <=time_2:
        if now <=time_2:
            return True
        return False

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if instance.criteria_type == 2 and hasattr(instance, 'attendsessioncriteria'):
            if self.check_times(instance.attendsessioncriteria):
                ret = AttendSessionCriteriaSerializer(instance.attendsessioncriteria).to_representation(instance.attendsessioncriteria)
                return ret
        elif instance.criteria_type == 3 and hasattr(instance, 'watchsessioncriteria'):
            if self.check_times(instance.watchsessioncriteria):
                ret = WatchSessionCriteriaSerializer(instance.watchsessioncriteria).to_representation(instance.watchsessioncriteria)
                return ret
        else:
            return ret