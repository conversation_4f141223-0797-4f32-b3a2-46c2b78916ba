from django.core.mail import send_mail
from django.template import Template, Context
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from rest_framework_simplejwt.tokens import RefreshToken




def get_tokens_for_user(user):
    refresh = RefreshToken.for_user(user)
    return str(refresh.access_token)


def get_role_types(subscriber):
    role_types = subscriber.role_type
    subscriber_roles = {
        'is_attendee': False,
        'is_attendant': False,
        'is_client': False,
        'is_support': False,
    }
    for role_type in role_types:
        if role_type == 1:
            subscriber_roles['is_attendee'] = True
        elif role_type == 2:
            subscriber_roles['is_attendant'] = True
        elif role_type == 3:
            subscriber_roles['is_client'] = True
        elif role_type == 4:
            subscriber_roles['is_support'] = True
    return subscriber_roles


def send_reg_confirm(event, user):
    if not event.registration_confirmation:
        return
    subject = 'Registration Confirmation'
    frontend_domain = ''
    sub_domain = ''
    if 'localhost' in event.domain_url:
        frontend_domain = 'localhost:3000'
    if 'mbvs-server-v2-dev2.us-east-1.elasticbeanstalk.com' in event.domain_url:
        frontend_domain = 's3-website-us-east-1.amazonaws.com'
        sub_domain = event.frontend_sub_domain
    if 'mbvs-server-development.com' in event.domain_url:
        frontend_domain = 'matchboxvirtualdevelopment.com'
        sub_domain = event.frontend_sub_domain
    if 'mbvs-server-staging.com' in event.domain_url:
        frontend_domain = 'matchboxvirtualstaging.com'
        sub_domain = event.frontend_sub_domain
    if 'mbvs-server.com' in event.domain_url:
        frontend_domain = 'matchboxvirtualspaces.com'
        sub_domain = event.frontend_sub_domain

    if event.reg_confirm_email_html:
        html_string = event.reg_confirm_email_html
        t = Template(html_string)
        c = Context({
            'event': event,
            'user': user,
            'frontend_domain': frontend_domain,
            'sub_domain': sub_domain,
        })
        html_message = t.render(c)

    else:
        email_template_name = 'send_registration_confim.html'
        context = {
            'event': event,
            'user': user,
            'frontend_domain': frontend_domain,
            'sub_domain': sub_domain,
        }
        html_message = render_to_string(email_template_name, context)

    message = strip_tags(html_message)
    send_mail(subject, message, 'Matchbox Support <<EMAIL>>', [user.email], fail_silently=False, html_message=html_message)
