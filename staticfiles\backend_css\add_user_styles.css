.top-button {
    display: flex;
    justify-content: space-between;
}
  
.back-btn, .logout-btn {
    background-color: #00253A;
    padding: 5px 20px;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    border: 2px #25bfff solid;
    border-radius: 20px;
    cursor: pointer;
    text-decoration: none;
}

.header-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 1.5rem;
}

.subheader {
    width: 80%;
}

form {
    margin-left: 2rem;
    margin-right: 2rem;
}

.select-container {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

.select-label, .input-label {
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    display: block; 
    margin-bottom: 0.5rem;
}

.select-container, .text-input-container {
    width: 35%
}

.submit-container {
    margin-bottom: 1rem;
}

.submit-btn {
    background-color: #00253A;
    padding: 5px 20px;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-transform: capitalize;
    border: 2px #25bfff solid;
    border-radius: 20px;
    cursor: pointer;
    text-decoration: none;
    margin-top: 30px;
    display: block;
    font-family: 'Open Sans', Arial;
}

.back-btn:hover, .submit-btn:hover {
    color: rgba(255, 255, 255, 0.5)
}


