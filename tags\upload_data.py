from helpers.imports.universal_helpers import  get_default_true
from helpers.imports.spaces_helpers import get_spaces
from helpers.imports.auditorium_helpers import get_auditoriums
from helpers.imports.subscriptions_helpers import get_subscribers
from helpers.imports.expohall_helpers import get_categories, get_tiles
from helpers.imports.tags_helpers import create_valid_tag_group

from rest_framework.views import APIView
from rest_framework import permissions
from django.contrib import messages
from django.shortcuts import render
import io, csv


class UploadTags(APIView):
    permission_classes = (permissions.IsAdminUser,)
    
    def get(self, request):
        template_name = 'event_upload/import_tags.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_tags.html'
        try:
            csv_file = request.FILES['tagfile']
        except:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')

        incomplete_items = []
        bad_tags = []
        reader = csv.DictReader(file)
        for row in reader:
            tag_category = row['tag_category']
            tag = row['tag']
            tile_names = row['tiles']
            space_names = row['spaces']
            auditorium_names = row['auditoriums']
            category_names = row['categories']
            emails = row['emails']
            permissions = row['permissions']
            filtering = row['filtering']

            tag_group = create_valid_tag_group(tag_category, tag)
            if not tag_group:
                bad_tags.append(tag_category + ': ' + tag)
                continue

            if tile_names != '':
                tile_list = tile_names.split(' \\ ')
                new_tiles = get_tiles(tile_list)
                if new_tiles.count() != len(tile_list):
                    incomplete_items.append(tag+ ': Tiles')
                tag_group.tiles.set(new_tiles)
            
            if space_names != '':
                space_list = space_names.split(' \\ ')    
                new_spaces = get_spaces(space_list)
                if new_spaces.count() != len(space_list):
                    incomplete_items.append(tag+ ': Spaces')
                tag_group.spaces.set(new_spaces)

            if category_names != '':
                categories_list = category_names.split(' \\ ')
                new_categories = get_categories(categories_list)
                if new_categories.count() != len(categories_list):
                    incomplete_items.append(tag+ ': Categories')
                tag_group.categories.set(new_categories)

            if auditorium_names != '':
                auditorium_list = auditorium_names.split(' \\ ')
                new_auds = get_auditoriums(auditorium_list)
                if new_auds.count() != len(auditorium_list):
                    incomplete_items.append(tag+ ': Auditoriums')
                tag_group.auditoriums.set(new_auds)

            if emails != '':
                email_list = emails.split(' \\ ')
                new_subs = get_subscribers(email_list)
                if new_subs.count() != len(email_list):
                    incomplete_items.append(tag+ ': Emails')
                tag_group.subscriptions.set(new_subs)

            if permissions != '':
                tag_group.permissions= get_default_true(permissions)
            if filtering != '':
                tag_group.filtering= get_default_true(filtering)
            tag_group.save()



        res = {
            'message': 'Success!',
            'incomplete_items': incomplete_items,
            'bad_tags': bad_tags
        }
        
        return render(request, template_name, res)