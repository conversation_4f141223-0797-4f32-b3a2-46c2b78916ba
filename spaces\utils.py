from .models import Space


def path_from_request(request):
    # split on `:` to remove port
    return request.path


def tenant_from_request(request):
    host = path_from_request(request)
    path = host.split("/")[0]
    return Space.objects.filter(path=path).first()


def check_event_default_space_id(instance, event):
    if not event.default_space_id:
        event.default_space_id = instance.id
        event.default_space_type = instance.space_type
        event.save()
    if event.default_space_id == str(instance.id) and event.default_space_type != instance.space_type:
        event.default_space_type = instance.space_type
        event.save()


def change_event_default_space_id(instance, event):
    space_ids = []
    space_types = []
    spaces = Space.objects.all()
    for space in spaces:
        if space.id != instance.id:
            space_ids.append(str(space.id))
            space_types.append(space.space_type)

    if space_ids and space_types:
        event.default_space_id = space_ids[0]
        event.default_space_type = space_types[0]
        event.save()
    if not space_ids and not space_types:
        event.default_space_id = None
        event.default_space_type = 'EXPO'
        event.save()


def export_boolean(bool_value):
    return str(bool_value).upper()


def show_list(list):
    values = [value.name for value in list]
    return " \\ ".join(values)


def join_with_backslash(values):
    return ' \\ '.join(values)

